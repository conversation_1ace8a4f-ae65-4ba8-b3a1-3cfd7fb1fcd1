import { DataTypeAbstract, DefineAttributeColumnOptions } from 'sequelize';
import { Authorization, User } from '@ordermentum/auth-driver';

declare global {
  type SequelizeAttributes<T extends { [key: string]: any }> = {
    [P in keyof T]: string | DataTypeAbstract | DefineAttributeColumnOptions;
  };

  namespace Express {
    interface Request {
      user?: User;
      auth?: Authorization;
      reqId: string;
    }
  }
}
