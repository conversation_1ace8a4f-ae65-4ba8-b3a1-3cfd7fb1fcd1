# Flags Service

Micro service to manage Feature flags for Ordermentum

## Setup

### With docker
```
docker-compose build

docker-compose run app bash

yarn

yarn db:setup

docker-compose up app
``` 

### Without docker (recommended)
```
source .env-local
yarn db:migrate
overmind start
```

Tourbleshooting
If you get an error saying 'database "flags_development" does not exist':
`yarn db:setup`

If you don't have overmind:
`brew install overmind`
