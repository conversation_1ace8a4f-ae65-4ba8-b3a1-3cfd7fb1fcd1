/* eslint-disable no-console */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
let baseDirectory;

if (['testing', 'production'].includes(process.env.NODE_ENV)) {
  baseDirectory = `../build`;
} else {
  require('@babel/register')({ extensions: ['.js', '.jsx', '.ts', '.tsx'] });
  baseDirectory = `..`;
}

const app = require(`${baseDirectory}/src`).default;
const env = require(`${baseDirectory}/src/config/env`);
const { logger } = require(`${baseDirectory}/src/config`);

const server = app.listen(env.PORT, () => {
  logger.info(`Listening on port ${env.PORT}...`);
});

const { createHttpTerminator } = require('http-terminator');

const httpTerminator = createHttpTerminator({
  server,
  gracefulTerminationTimeout: 30000,
});

for (const signal of ['SIGTERM', 'SIGINT']) {
  process.on(signal, async () => {
    console.log(`Received ${signal}, stopping server`);
    await httpTerminator.terminate();
    process.exit();
  });
}
