#!/bin/bash

psql $DATABASE_INITIALIZATION_URI -v ON_ERROR_STOP=0 -t <<-EOSQL
  CREATE USER ordermentum;
  ALTER USER ordermentum WITH SUPERUSER;

  CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
EOSQL

psql $DATABASE_INITIALIZATION_URI -tc "SELECT 1 FROM pg_database WHERE datname = 'flags_development'" | grep -q 1 || psql $DATABASE_INITIALIZATION_URI -c "CREATE DATABASE flags_development"

psql $DATABASE_INITIALIZATION_URI -tc "SELECT 1 FROM pg_database WHERE datname = 'flags_testing'" | grep -q 1 || psql $DATABASE_INITIALIZATION_URI -c "CREATE DATABASE flags_testing"
