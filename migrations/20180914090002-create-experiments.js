module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('experiments', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: '',
      },
      slot_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: { model: 'slots', key: 'id' },
        onUpdate: 'cascade',
        onDelete: 'cascade',
      },
      meta: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
      },

      priority: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      exclusive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },

      limit: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      interval: {
        type: Sequelize.ENUM('total', 'day', 'week', 'month', 'year'),
        allowNull: false,
        defaultValue: 'total',
      },

      start_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      end_at: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null,
      },

      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      deleted_at: {
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex('experiments', ['name']);
  },

  down: async queryInterface => {
    await queryInterface.dropTable('experiments');
  },
};
