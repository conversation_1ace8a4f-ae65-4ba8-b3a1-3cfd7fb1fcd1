module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('flags', 'user_id', {
      type: Sequelize.UUID,
      allowNull: true,
      defaultValue: null,
    });
    await queryInterface.addColumn('flags', 'updated_by_id', {
      type: Sequelize.UUID,
      allowNull: true,
      defaultValue: null,
    });
    // add same columns to experiments table
    await queryInterface.addColumn('experiments', 'user_id', {
      type: Sequelize.UUID,
      allowNull: true,
      defaultValue: null,
    });
    await queryInterface.addColumn('experiments', 'updated_by_id', {
      type: Sequelize.UUID,
      allowNull: true,
      defaultValue: null,
    });
    // add for audience
    await queryInterface.addColumn('audiences', 'user_id', {
      type: Sequelize.UUID,
      allowNull: true,
      defaultValue: null,
    });
    await queryInterface.addColumn('audiences', 'updated_by_id', {
      type: Sequelize.UUID,
      allowNull: true,
      defaultValue: null,
    });
  },
  async down(queryInterface) {
    await queryInterface.removeColumn('experiments', 'user_id');
    await queryInterface.removeColumn('experiments', 'updated_by_id');
    await queryInterface.removeColumn('audiences', 'user_id');
    await queryInterface.removeColumn('audiences', 'updated_by_id');
    await queryInterface.removeColumn('flags', 'updated_by_id');
    return queryInterface.removeColumn('flags', 'user_id');
  },
};
