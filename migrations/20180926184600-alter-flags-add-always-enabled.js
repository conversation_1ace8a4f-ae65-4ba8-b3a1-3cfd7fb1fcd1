module.exports = {
  up(queryInterface, Sequelize) {
    const sql = `ALTER TABLE flags
    ADD always_enabled BOOLEAN NOT NULL DEFAULT false`;

    return queryInterface.sequelize.query(sql, {
      type: Sequelize.QueryTypes.RAW,
      raw: true,
    });
  },
  down(queryInterface, Sequelize) {
    const sql = `ALTER TABLE flags
    DROP COLUMN always_enabled`;

    return queryInterface.sequelize.query(sql, {
      type: Sequelize.QueryTypes.RAW,
      raw: true,
    });
  },
};
