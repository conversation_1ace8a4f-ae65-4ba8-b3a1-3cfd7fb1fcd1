module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('flag_audiences', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      flag_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'flags',
          key: 'id',
        },
        onUpdate: 'cascade',
        onDelete: 'cascade',
      },
      audience_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'audiences',
          key: 'id',
        },
        onUpdate: 'cascade',
        onDelete: 'cascade',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      deleted_at: {
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex(
      'flag_audiences',
      ['flag_id', 'audience_id'],
      {
        indicesType: 'UNIQUE',
      }
    );
  },

  down: async queryInterface => {
    await queryInterface.dropTable('flag_audiences');
  },
};
