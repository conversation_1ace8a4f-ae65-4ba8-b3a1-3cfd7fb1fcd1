module.exports = {
  async up(queryInterface, Sequelize) {
    const sql = `ALTER TABLE promotions
    ADD CONSTRAINT unique_coupon_per_host
    UNIQUE(host_id, coupon)`;

    return queryInterface.sequelize.query(sql, {
      type: Sequelize.QueryTypes.RAW,
      raw: true,
    });
  },
  async down(queryInterface, Sequelize) {
    const sql = `ALTER TABLE promotions
    DROP CONSTRAINT IF EXISTS unique_coupon_per_host`;

    return queryInterface.sequelize.query(sql, {
      type: Sequelize.QueryTypes.RAW,
      raw: true,
    });
  },
};
