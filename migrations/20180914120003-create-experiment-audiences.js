module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('experiment_audiences', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      experiment_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'experiments',
          key: 'id',
        },
        onUpdate: 'cascade',
        onDelete: 'cascade',
      },
      audience_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'audiences',
          key: 'id',
        },
        onUpdate: 'cascade',
        onDelete: 'cascade',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      deleted_at: {
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex(
      'experiment_audiences',
      ['experiment_id', 'audience_id'],
      {
        indicesType: 'UNIQUE',
      }
    );
  },

  down: async queryInterface => {
    await queryInterface.dropTable('experiment_audiences');
  },
};
