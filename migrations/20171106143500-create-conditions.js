module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('conditions', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      key: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: '',
      },
      rule_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: { model: 'rules', key: 'id' },
        onUpdate: 'cascade',
        onDelete: 'cascade',
      },
      negate: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      lval: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: [],
      },
      operator: {
        type: Sequelize.ENUM('equals', 'includes'),
        allowNull: false,
        defaultValue: 'equals',
      },
      rval: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: [],
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      deleted_at: { type: Sequelize.DATE },
    });
  },

  down: async queryInterface => {
    await queryInterface.dropTable('conditions');
  },
};
