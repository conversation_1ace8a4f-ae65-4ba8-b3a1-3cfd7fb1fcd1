module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('promotions_usage', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      promotion_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: { model: 'promotions', key: 'id' },
      },
      supplier_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      retailer_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      purchaser_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
      },
      deleted_at: {
        type: Sequelize.DATE,
        defaultValue: null,
        allowNull: true,
      },
    });
  },

  down(queryInterface) {
    return queryInterface.dropTable('promotions_usage');
  },
};
