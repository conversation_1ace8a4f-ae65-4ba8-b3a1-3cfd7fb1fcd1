module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('promotions', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      name: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: false,
        defaultValue: Sequelize.TEXT,
      },
      conditions: {
        type: Sequelize.JSONB,
        allowNull: false,
      },
      effects: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: '[]',
      },
      host_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      created_by_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      updated_by_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      coupon: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      start_at: {
        type: Sequelize.DATE,
        defaultValue: null,
        allowNull: true,
      },
      finish_at: {
        type: Sequelize.DATE,
        defaultValue: null,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: null,
        allowNull: true,
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: null,
        allowNull: true,
      },
      deleted_at: {
        type: Sequelize.DATE,
        defaultValue: null,
        allowNull: true,
      },
    });

    await queryInterface.addConstraint('promotions', {
      type: 'unique',
      fields: ['host_id', 'name', 'deleted_at'],
    });

    return null;
  },

  down(queryInterface) {
    return queryInterface.dropTable('promotions');
  },
};
