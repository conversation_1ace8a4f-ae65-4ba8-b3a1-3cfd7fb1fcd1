module.exports = {
  up(queryInterface, Sequelize) {
    const sql = `ALTER TABLE flags
    ADD CONSTRAINT flag_unique_name
    UNIQUE(name)`;

    return queryInterface.sequelize.query(sql, {
      type: Sequelize.QueryTypes.RAW,
      raw: true,
    });
  },
  down(queryInterface, Sequelize) {
    const sql = `ALTER TABLE flags
    DROP CONSTRAINT IF EXISTS flag_unique_name`;

    return queryInterface.sequelize.query(sql, {
      type: Sequelize.QueryTypes.RAW,
      raw: true,
    });
  },
};
