module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `ALTER TYPE enum_conditions_operator
      ADD VALUE 'not_includes'`,
      {
        type: Sequelize.QueryTypes.RAW,
        raw: true,
      }
    );

    return queryInterface.sequelize.query(
      `ALTER TYPE enum_conditions_operator
      ADD VALUE 'not_equals'`,
      {
        type: Sequelize.QueryTypes.RAW,
        raw: true,
      }
    );
  },
};
