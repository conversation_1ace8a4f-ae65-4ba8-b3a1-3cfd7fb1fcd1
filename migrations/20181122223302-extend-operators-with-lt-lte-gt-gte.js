module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `ALTER TYPE enum_conditions_operator
      ADD VALUE 'lt'`,
      {
        type: Sequelize.QueryTypes.RAW,
        raw: true,
      }
    );

    await queryInterface.sequelize.query(
      `ALTER TYPE enum_conditions_operator
      ADD VALUE 'gt'`,
      {
        type: Sequelize.QueryTypes.RAW,
        raw: true,
      }
    );

    await queryInterface.sequelize.query(
      `ALTER TYPE enum_conditions_operator
      ADD VALUE 'lte'`,
      {
        type: Sequelize.QueryTypes.RAW,
        raw: true,
      }
    );

    return queryInterface.sequelize.query(
      `ALTER TYPE enum_conditions_operator
      ADD VALUE 'gte'`,
      {
        type: Sequelize.QueryTypes.RAW,
        raw: true,
      }
    );
  },
};
