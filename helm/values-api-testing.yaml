replicaCount: 1
nameOverride: flags
readinessCheckUrl: "/live"
image:
  repository: 581666996624.dkr.ecr.us-east-1.amazonaws.com/ordermentum/flags
  tag: develop
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  externalPort: 3333
  internalPort: 3333
  enabled: true
ingress:
  enabled: true
  hosts:
    - flags.ordermentum-sandbox.com
    - flags-testing.ordermentum.com
  annotations:
    nginx.ingress.kubernetes.io/limit-rps: "10"
podAnnotations:
resources:
  limits:
    memory: 192Mi
  requests:
    cpu: 80m
    memory: 120Mi
cpuHPA:
  averageValue: 100 # 80m
  minReplicas: 2
  maxReplicas: 16
env:
  nodeEnv:
    name: "NODE_ENV"
    value: "testing"
  authUrl:
    name: "AUTH_URL"
    value: "https://auth.ordermentum-sandbox.com"
  logLevel:
    name: "LOG_LEVEL"
    value: "info"
  sentryDsn:
    name: "SENTRY_DSN"
    value: "https://1c1855bb97314aa198672da168148da7:<EMAIL>/29290"
  redisUrl:
    name: "REDIS_URL"
    value: "master.redis-main.hzrisb.use1.cache.amazonaws.com"
  redisUseTls:
    name: "REDIS_USE_TLS"
    value: "true"
  tz:
    name: "TZ"
    value: "Australia/Sydney"
  asapPublicKeysUrl:
    name: "ASAP_PUBLIC_KEYS_URL"
    value: "https://ordermentum-service-public-keys-sandbox.s3.us-east-1.amazonaws.com/"
  datadogEnvironment:
    name: DD_ENV
    value: sandbox
  datadogServiceName:
    name: DD_SERVICE
    value: flags
  datadogServiceVersion:
    name: DD_VERSION
    value: ""
  datadogEntityId:
    name: DD_ENTITY_ID
    valueFrom:
      fieldRef:
        apiVersion: v1
        fieldPath: metadata.uid
  nodeOptions:
    name: NODE_OPTIONS
    value: ' --require=dd-trace/init'
externalSecrets:
  - DATABASE_URI
  - SECRET_KEY
  - SEGMENT_WRITE_KEY
  - JWT_SECRET
  - KAFKA_BOOTSTRAP_SERVERS
  - REDIS_PASSWORD
secretsManagerSecretName: flags
deployment:
  podLabels:
    tags.datadoghq.com/env: sandbox
    tags.datadoghq.com/service: flags
  additionalPodAnnotations: {}
podDisruptionBudget:
  enabled: true
  minAvailable: 1
