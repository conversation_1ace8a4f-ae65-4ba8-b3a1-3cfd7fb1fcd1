replicaCount: 4
nameOverride: flags
readinessCheckUrl: "/live"
image:
  repository: 581666996624.dkr.ecr.ap-southeast-2.amazonaws.com/ordermentum/flags
  tag: master
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  externalPort: 3333
  internalPort: 3333
  enabled: true
ingress:
  enabled: true
  hosts:
    - flags.ordermentum.com
  annotations:
    # Ideally we would set RPS per pod to allow for a graceful ramp up. Since
    # we can't do this, we just need to be conservative.
    # Each pod can handle at most ~30 rps.
    nginx.ingress.kubernetes.io/limit-rps: "150"
podAnnotations:
resources:
  requests:
    cpu: 500m
    memory: 188Mi
  limits:
    memory: 470Mi
cpuHPA:
  averageValue: 60 # 150m
  minReplicas: 2
  maxReplicas: 16
env:
  nodeEnv:
    name: "NODE_ENV"
    value: "production"
  logLevel:
    name: "LOG_LEVEL"
    value: "info"
  authUrl:
    name: "AUTH_URL"
    value: "https://auth.ordermentum.com"
  sentryDsn:
    name: "SENTRY_DSN"
    value: "https://1c1855bb97314aa198672da168148da7:<EMAIL>/29290"
  redisUrl:
    name: "REDIS_URL"
    value: master.redis-main.oeeadg.apse2.cache.amazonaws.com
  redisUseTls:
    name: "REDIS_USE_TLS"
    value: "true"
  tz:
    name: "TZ"
    value: "Australia/Sydney"
  asapPublicKeysUrl:
    name: "ASAP_PUBLIC_KEYS_URL"
    value: "https://ordermentum-service-public-keys-production.s3.ap-southeast-2.amazonaws.com/"
  datadogEnvironment:
    name: DD_ENV
    value: production
  datadogServiceName:
    name: DD_SERVICE
    value: flags
  datadogServiceVersion:
    name: DD_VERSION
    value: ""
  datadogEntityId:
    name: DD_ENTITY_ID
    valueFrom:
      fieldRef:
        apiVersion: v1
        fieldPath: metadata.uid
  nodeOptions:
    name: NODE_OPTIONS
    value: ' --require=dd-trace/init'
externalSecrets:
  - DATABASE_URI
  - SECRET_KEY
  - SEGMENT_WRITE_KEY
  - JWT_SECRET
  - NEW_RELIC_LICENSE_KEY
  - KAFKA_BOOTSTRAP_SERVERS
  - REDIS_PASSWORD
secretsManagerSecretName: flags
deployment:
  podLabels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/service: flags
  additionalPodAnnotations: {}
podDisruptionBudget:
  enabled: true
  minAvailable: 1
