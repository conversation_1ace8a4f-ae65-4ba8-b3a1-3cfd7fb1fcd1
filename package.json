{"name": "flags-service", "version": "1.0.0", "description": "Feature Flags service for Ordermentum ", "main": "build/src/index.js", "repository": "**************:ordermentum/flags.git", "author": "<EMAIL>", "license": "MIT", "scripts": {"build:clean": "rm -rf build", "build": "yarn build:clean && tsc", "precommit": "lint-staged", "start:dev": "yarn build && ./bin/app", "start": "node --max-http-header-size=65536 ./bin/app", "lint": "yarn eslint '**/*.{ts,js}'", "test": "yarn db:setup && yarn spec", "spec": "yarn run mocha --config .mocharc.local.yml -R spec -t 7000 'test/**/*.{js,ts}'", "spec:local": "source .env-test && yarn run mocha --config .mocharc.local.yml -R spec -t 7000", "db:init": "./bin/initialize", "db:reset": "./bin/reset", "db:setup": "./bin/initialize && ./bin/migrate", "db:migration": "sequelize migration:create --url `echo $DATABASE_URI`", "typecheck": "yarn tsc --noEmit", "db:migrate": "sequelize db:migrate --url `echo $DATABASE_URI`", "start:dev:local": "source .env-local && babel-node --max-http-header-size=65536 --extensions '.ts' bin/app | ./node_modules/.bin/bunyan", "watch:dev:local": "watchexec -w src -r yarn start:dev:local", "watch:app:local": "watchexec -w src -r yarn start:dev:local", "topics:setup:local": "source .env-local && babel-node --extensions '.ts' ./src/utils/steveo/create_topics.ts"}, "husky": {"hooks": {"pre-commit": "yarn typecheck && lint-staged"}}, "lint-staged": {"*.{ts,js}": ["yarn run eslint --fix --max-warnings 0 --no-ignore"]}, "dependencies": {"@hapi/joi": "^17.1.1", "@hapi/joi-date": "^2.0.1", "@ordermentum/auth-driver": "^7.2.0", "@ordermentum/auth-middleware": "^4.0.0", "@ordermentum/cache-machine": "^2.3.1", "@ordermentum/express-asap": "1.0.0", "@ordermentum/fireflight": "^0.0.6", "@ordermentum/prowl": "^0.3.4", "@ordermentum/serdes_changelog": "^2.0.3", "@ordermentum/serdes_promotion": "^2.0.3", "@ordermentum/slingshot": "^0.2.1", "@segment/analytics-node": "^2.2.1", "@sentry/node": "6.19.7", "@types/connect-datadog": "^0.0.4", "@types/node-dogstatsd": "^0.0.9", "asclepius-standard": "^1.0.2", "body-parser": "^1.18.2", "bunyan": "^1.8.12", "bunyan-middleware": "^1.0.0", "connect-datadog": "^0.0.9", "dd-trace": "4", "dotenv-safe": "^4.0.4", "express": "^4.16.2", "express-async-errors": "^3.0.0", "express-bearer-token": "^2.1.0", "express-http-context": "^1.2.4", "handlebars": "^4.0.12", "http-errors": "^1.6.2", "http-terminator": "^3.2.0", "ioredis": "5.7.0", "joi-tz": "^4.0.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.4", "maccabee": "^0.1.0", "moment": "^2.27.0", "node-dogstatsd": "^0.0.7", "pg": "^8.0.0", "pg-hstore": "^2.3.4", "qs": "^6.5.2", "sequelize": "6.37.5", "steveo": "^5.0.0-beta2.3", "useragent": "^2.3.0", "uuid": "^3.1.0"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/node": "7.28.0", "@babel/preset-env": "7.28.0", "@babel/preset-typescript": "7.27.1", "@babel/register": "7.27.1", "@types/bunyan": "1.8.7", "@types/chai": "4.3.20", "@types/express": "4.17.23", "@types/faker": "5.5.9", "@types/hapi__joi": "17.1.15", "@types/http-errors": "1.8.2", "@types/jsonwebtoken": "8.5.9", "@types/lodash": "4.17.20", "@types/mocha": "9.1.1", "@types/node": "10.17.60", "@types/qs": "6.14.0", "@types/sequelize": "4.28.20", "@types/sinon": "10.0.20", "@types/supertest": "2.0.16", "@types/useragent": "2.3.4", "@types/uuid": "3.4.13", "chai": "4.5.0", "co-mocha": "1.2.2", "eslint-config-ordermentum": "1.0.3", "faker": "5.5.3", "husky": "4.3.8", "lint-staged": "11.2.6", "mocha": "9.2.2", "sequelize-cli": "5.5.1", "sinon": "11.1.2", "supertest": "6.3.3", "ts-node": "^10.9.1", "typescript": "4.9.5"}, "resolutions": {"@ordermentum/hapi-asap": "^1.0.0"}, "packageManager": "yarn@1.22.22"}