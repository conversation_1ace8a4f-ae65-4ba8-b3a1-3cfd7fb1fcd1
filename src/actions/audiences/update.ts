import _ from 'lodash';
import httpErrors from 'http-errors';
import models, { Condition } from '../../models';
import validator from '../../validations/audiences';
import { updateAudienceConditions } from './helper';
import read from './read';
import { ConditionInstance } from '../../models/condition';

const update = async (body, id) => {
  const audience = await models.Audience.findOne({
    where: {
      id,
    },
    include: [
      {
        model: models.Condition,
        as: 'conditions',
      },
    ],
  });

  if (!audience) {
    throw new httpErrors.NotFound();
  }

  const conditions = audience.conditions || [];

  // create a copy of conditions, because validator will mutate nested objects
  const existingConditions: any[] = conditions.map(c => c.get({ plain: true }));

  try {
    const validated = await validator(body, audience);

    const incomingConditions = validated.conditions;

    await validated.save();

    if (body.conditions) {
      await updateAudienceConditions({
        existing: existingConditions,
        incoming: incomingConditions,
        audienceId: audience.id,
      });
    }
    return read(validated.id);
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default update;
