import _ from 'lodash';
import models from '../../models';

export const buildData = (
  existing: Array<string>,
  incoming: Array<string>
) => ({
  toDelete: _.difference(existing, incoming).filter(v => v),
  toUpdate: _.intersection(incoming, existing).filter(v => v),
});

export const updateAudienceConditions = async ({
  existing,
  incoming,
  audienceId,
}) => {
  const existingConditionIds = existing.map(c => c.id);
  const incomingConditionIds = incoming.map(c => c.id);

  const data = buildData(existingConditionIds, incomingConditionIds);

  /* eslint-disable no-await-in-loop */
  for (const id of data.toUpdate) {
    const condition = await models.Condition.findByPk(id);
    const payload = incoming.find(c => c.id === id);
    await condition.update(payload);
  }

  await models.Condition.destroy({
    where: {
      id: data.toDelete,
    },
  });

  const toCreate = incoming
    .filter(c => c.id == null)
    .map(c => ({
      ...c,
      audienceId,
    }));
  await models.Condition.bulkCreate(toCreate);

  return true;
};
