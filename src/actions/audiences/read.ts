import httpErrors from 'http-errors';
import models from '../../models';
import AudienceViewModel from '../../view_models/audience';

const read = async id => {
  const audience = await models.Audience.findOne({
    include: [{ model: models.Condition, as: 'conditions' }],
    where: {
      id,
    },
  });

  if (!audience) {
    throw new httpErrors.NotFound();
  }
  return AudienceViewModel.build(audience);
};

export default read;
