import httpErrors from 'http-errors';
import models from '../../models';
import validator from '../../validations/audiences';
import { updateAudienceConditions } from './helper';
import read from './read';

const create = async body => {
  try {
    const validated = await validator(body, models.Audience.build());

    const audience = await validated.save();

    if (body.flagId) {
      const attrs = {
        flagId: body.flagId,
        audienceId: audience.id,
      };
      await models.FlagAudience.create(attrs);
    }

    if (body.conditions) {
      await updateAudienceConditions({
        existing: [],
        incoming: validated.conditions,
        audienceId: audience.id,
      });
    }
    return read(validated.id);
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default create;
