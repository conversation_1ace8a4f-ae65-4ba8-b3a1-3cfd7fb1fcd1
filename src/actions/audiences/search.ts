import buildPagination from '@ordermentum/fireflight';
import models from '../../models';
import AudienceViewModel from '../../view_models/audience';
import buildQuery from '../../lib/sequelize';

const DEFAULTS = {
  pageSize: 50,
  pageNo: 1,
  offset: 0,
};

const search = async raw => {
  const pageSize = raw.pageSize || DEFAULTS.pageSize;
  const pageNo = raw.pageNo || DEFAULTS.pageNo;

  const query = buildQuery(raw);

  const audiences = await models.Audience.findAll(query);

  const totalResults = await models.Audience.count();

  const data = AudienceViewModel.build(audiences);

  const results = {
    baseUrl: '/v1/audiences',
    data: data.toJSON(),
    totalResults,
    pageSize,
    query: {
      pageNo,
      pageSize,
    },
    pageNo,
  };

  const paginated = buildPagination(results).paginate();
  return paginated;
};

export default search;
