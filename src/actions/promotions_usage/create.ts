import Joi from '@hapi/joi';
import { BadRequest, NotFound } from 'http-errors';
import { PromotionsUsage, Promotion } from '../../models';

export type Discounts = {
  name: string;
  coupon: string;
  sponsored: boolean;
  hostId: string;
};

export type Payload = {
  retailerId: string;
  supplierId: string;
  userId: string;
  purchaserId: string;
  discounts?: Discounts[];
};

export type UsageCreateParams = Payload & {
  promotionId: string;
};

export const usageSchema = Joi.object<UsageCreateParams>({
  promotionId: Joi.string().guid().required(),
  supplierId: Joi.string().guid().required(),
  retailerId: Joi.string().guid().required(),
  userId: Joi.string().guid().required(),
  purchaserId: Joi.string().guid().required(),
});

export async function create(payload: Payload) {
  const { discounts, supplierId, userId, retailerId, purchaserId } = payload;
  const usageData = await Promise.all(
    discounts.map(async discount => {
      const { name, coupon, hostId, sponsored = false } = discount;
      const promotion = await Promotion.findOne({
        where: { coupon, name, hostId, sponsored },
      });

      if (!promotion) {
        throw new NotFound('Promotion not found');
      }

      const {
        value,
        error,
      }: {
        value: UsageCreateParams;
        error?: Joi.ValidationError;
      } = usageSchema.validate({
        supplierId,
        retailerId,
        purchaserId,
        userId,
        promotionId: promotion.id,
      });

      if (error) {
        throw new BadRequest(error.message);
      }

      return { ...value };
    })
  );

  const result = await PromotionsUsage.bulkCreate(usageData);

  return result;
}
