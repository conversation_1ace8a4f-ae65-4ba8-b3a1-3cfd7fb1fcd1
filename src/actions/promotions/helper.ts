import { get, set } from 'lodash';

export type Filter = {
  field: {
    tag: string;
    isProperty: boolean;
  };
  operator: string;
  value: string | string[];
};

const filterCombinatorMap = {
  equals: 'equal',
};

const filterModifierMap = {
  lastOrderedSince: '_access.lastOrderedSince',
  hasOrdered: '_access.hasOrdered',
  firstOrder: '_access.firstOrder',
  paymentType: '_access.defaultPaymentMethodType',
  purchaser: '_access.id',
  supplierCategory: '_access.dataAttributes.primaryCategory',
};

const filterLeftNodeValueMap = {
  lastOrderedSince: 'purchaser',
  hasOrdered: 'purchaser',
  firstOrder: 'purchaser',
  cartTotal: 'total',
  paymentType: 'purchaser',
  purchaser: 'purchaser',
  cartSubtotal: 'totalCost',
  freightTotal: 'totalFreight',
  supplierCategory: 'supplier',
  firstOrderInCategory: 'firstOrderInCategory',
};

const createPropertyNode = (filter: Filter) => ({
  leftNode: 'purchaser',
  rightNode: filter.value,
  modifier: `_access.properties.${filter.field.tag}`,
  combinator: filterCombinatorMap[filter.operator] || filter.operator,
  tag: filter.field.tag,
});

export const parseConditions = (filters: any, supplierId) => {
  const initialConditions = {
    leftNode: {
      leftNode: null,
      rightNode: null,
      combinator: 'and',
    },
    rightNode: {
      leftNode: 'supplierId',
      rightNode: supplierId,
      combinator: 'equal',
    },
    combinator: 'and',
  };

  let conditions: any = null;
  if (filters.length === 1) {
    const filter = filters[0];
    const field = filter.field.tag;
    const isProperty = filter.field.isProperty ?? false;

    const node = isProperty
      ? createPropertyNode(filter)
      : {
          leftNode: filterLeftNodeValueMap[field] ?? field,
          rightNode: filter.value,
          combinator: filterCombinatorMap[filter.operator] ?? filter.operator,
          modifier: filterModifierMap[field] ?? undefined,
          tag: field,
        };

    initialConditions.leftNode = node;
    conditions = initialConditions;
  } else {
    conditions = filters.reduce((acc: any, current: Filter, index: number) => {
      const field = current.field.tag;
      const isProperty = current.field.isProperty ?? false;

      const node = isProperty
        ? createPropertyNode(current)
        : {
            leftNode: filterLeftNodeValueMap[field] ?? field,
            rightNode: current.value,
            combinator:
              filterCombinatorMap[current.operator] || current.operator,
            modifier: filterModifierMap[field] ?? undefined,
            tag: field,
          };

      if (index === 0) {
        const leftNode = {
          leftNode: node,
          rightNode: true,
          combinator: 'and',
        };
        set(acc, 'leftNode', leftNode);
      } else if (index === 1) {
        set(acc, 'leftNode.rightNode', node);
      } else {
        const previousLeftNode = index > 0 ? get(acc, 'leftNode', null) : null;

        const rightNode = { ...previousLeftNode } ?? true;

        const newLeftNode = {
          leftNode: node,
          rightNode,
          combinator: 'and',
        };

        set(acc, 'leftNode', newLeftNode);
      }

      return acc;
    }, initialConditions);
  }

  return conditions;
};
