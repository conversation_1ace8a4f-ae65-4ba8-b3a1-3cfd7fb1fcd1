import { Authorization } from '@ordermentum/auth-driver';
import joi from '@hapi/joi';
import { BadRequest } from 'http-errors';
import { isArray } from 'lodash';
import { Promotion } from '../../models';
import { CombinatorEnum } from '../../models/promotion';
import { Filter, parseConditions } from './helper';
import { hasPromotionsCreatePrivilege } from '../../privileges/promotions_privileges';
import PromotionViewModel from '../../view_models/promotion';
import kafkaMethods, { Topics } from '../../utils/steveo/register_change';

const node = joi
  .alternatives()
  .try(joi.link('#condition'), joi.string(), joi.array(), joi.number());

export const condition = joi
  .object({
    combinator: joi.string().valid(...Object.keys(CombinatorEnum)),
    leftNode: node,
    rightNode: node,
    modifier: joi.string().trim(),
    negate: joi.bool(),
    tag: joi.string().trim(),
  })
  .id('condition');

export const filterValidator = joi
  .array()
  .items(
    joi.object({
      field: joi.object({
        tag: joi.string().trim().required(),
        isProperty: joi.boolean(),
      }),
      operator: joi.string().trim().required(),
      value: joi
        .alternatives()
        .try(
          joi.string().trim().required(),
          joi.array().items(joi.string().trim().required()).required()
        ),
    })
  )
  .min(1)
  .required();

const validator = joi.object({
  hostId: joi
    .string()
    .guid()
    .optional()
    .description('This will be either a supplierId or retailerId.'),
  name: joi.string().required(),
  description: joi.string().allow(''),
  startAt: joi.string().isoDate(),
  finishAt: joi.string().isoDate(),
  conditions: joi.when('coupon', {
    is: joi.exist(),
    then: joi
      .alternatives()
      .try(condition, filterValidator, joi.array().items()),
    otherwise: joi.alternatives().try(condition, filterValidator),
  }),
  effects: joi.array(),
  coupon: joi.string().trim().allow(null, '').uppercase(),
  usage: joi.number().allow(null),
  sponsored: joi.boolean().optional().default(false),
});

type Data = {
  hostId?: string;
  name: string;
  description: string;
  effects: any;
  conditions: any | Filter[];
  sponsored?: boolean;
  startAt: string;
  finishAt: string;
  coupon?: string;
  usage: number | null;
};

export default async function create(auth: Authorization, payload) {
  const { value, error }: { error?: any; value: Data } = validator.validate(
    payload,
    {
      allowUnknown: true,
    }
  );

  if (error) {
    throw new BadRequest(error);
  }

  hasPromotionsCreatePrivilege(auth, value.hostId);

  const validatedValue = value;
  if (isArray(value.conditions) && value.conditions.length) {
    const parsedConditions = parseConditions(value.conditions, value.hostId);
    const validateResult = condition.validate(parsedConditions, {
      allowUnknown: true,
    });

    if (validateResult.error) {
      throw new BadRequest(validateResult.error.message);
    }

    validatedValue.conditions = validateResult.value;
  }

  // The any type is needed because hostId is not always present
  // and the type of whereClause is not known
  const whereClause: any = {
    coupon: validatedValue.coupon,
  };

  // The hostId/supplierId will not exist
  // if the promotion is usable across all suppliers
  if (validatedValue.hostId) {
    whereClause.hostId = validatedValue.hostId;
  }

  if (validatedValue.coupon) {
    const couponPromotion = await Promotion.findOne({
      where: whereClause,
    });
    if (couponPromotion) {
      throw new BadRequest('Coupon code exists already');
    }
  }

  const promotion = await Promotion.create({
    ...validatedValue,
    createdById: auth.id,
    updatedById: auth.id,
  });

  const current = PromotionViewModel.build(promotion).toEventModel();
  kafkaMethods.registerChange(Topics.PROMOTIONS, current);
  return promotion;
}
