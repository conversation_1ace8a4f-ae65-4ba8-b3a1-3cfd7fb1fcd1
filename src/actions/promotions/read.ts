import { Authorization } from '@ordermentum/auth-driver';
import httpErrors from 'http-errors';
import { Promotion } from '../../models';
import PromotionViewModel from '../../view_models/promotion';
import { hasPromotionsReadPrivilege } from '../../privileges/promotions_privileges';

export default async function read(auth: Authorization, id: string) {
  const promotion = await Promotion.findByPk(id);

  if (!promotion) {
    throw new httpErrors.NotFound();
  }

  hasPromotionsReadPrivilege(auth, promotion.hostId);

  const data = PromotionViewModel.build(promotion);

  return data;
}
