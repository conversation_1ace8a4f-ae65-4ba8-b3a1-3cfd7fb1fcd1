import joi from '@hapi/joi';
import { NotFound, BadRequest, Forbidden } from 'http-errors';
import { isArray } from 'lodash';
import { Authorization } from '@ordermentum/auth-driver';
import { Promotion } from '../../models';
import PromotionViewModel from '../../view_models/promotion';
import { condition, filterValidator } from './create';
import { hasScope } from '../../policies/supplier_policy';
import { parseConditions } from './helper';
import { hasPromotionsUpdatePrivilege } from '../../privileges/promotions_privileges';
import kafkaMethods, { Topics } from '../../utils/steveo/register_change';

const validator = joi.object({
  name: joi.string().trim(),
  description: joi.string().trim().allow(''),
  startAt: joi.string().isoDate(),
  finishAt: joi.string().isoDate(),
  conditions: joi.when('coupon', {
    is: joi.exist(),
    then: joi
      .alternatives()
      .try(condition, filterValidator, joi.array().items()),
    otherwise: joi.alternatives().try(condition, filterValidator),
  }),
  effects: joi.array(),
  coupon: joi.string().trim().allow(null, '').uppercase(),
  sponsored: joi.boolean().optional(),
});

export default async function update(
  auth: Authorization,
  id: string,
  body: any
) {
  const { value, error }: { error?: any; value: any } = validator.validate(
    body,
    { allowUnknown: true }
  );

  if (error) {
    throw new BadRequest(error.message);
  }

  const promotion = await Promotion.findByPk(id);
  // Snapshot promotion to use for Serdes changelog serialization
  const previous = promotion.toJSON();

  if (!promotion) {
    throw new NotFound('Promotion not found');
  }

  hasPromotionsUpdatePrivilege(auth, promotion.hostId);

  const validatedValue = value;
  if (isArray(value.conditions) && value.conditions.length) {
    const parsedConditions = parseConditions(
      value.conditions,
      promotion.hostId
    );
    const validateResult = condition.validate(parsedConditions, {
      allowUnknown: true,
    });

    if (validateResult.error) {
      throw new BadRequest(validateResult.error.message);
    }

    validatedValue.conditions = validateResult.value;
  }

  const result = await promotion.update({
    ...validatedValue,
    updatedById: auth.id,
  });

  const data = PromotionViewModel.build(result);
  const current = data.toEventModel();
  kafkaMethods.registerChange(Topics.PROMOTIONS, current, previous);

  return data;
}
