import { Authorization } from '@ordermentum/auth-driver';
import { NotFound } from 'http-errors';
import { Promotion } from '../../models';
import { hasPromotionsFullPrivilege } from '../../privileges/promotions_privileges';

export default async function (auth: Authorization, id: string) {
  const promotion = await Promotion.findByPk(id);

  if (!promotion) {
    throw new NotFound('Promotion Not Found.');
  }

  hasPromotionsFullPrivilege(auth, promotion.hostId);

  return promotion.destroy();
}
