import buildPagination from '@ordermentum/fireflight';
import { Authorization } from '@ordermentum/auth-driver';

import Joi from '@hapi/joi';
import { BadRequest, Forbidden } from 'http-errors';
import { Promotion } from '../../models';
import PromotionViewModel from '../../view_models/promotion';
import { buildQuery } from '../../lib/query_builder';
import { hasScope } from '../../policies/supplier_policy';
import { hasPromotionsReadPrivilege } from '../../privileges/promotions_privileges';

const DEFAULTS = {
  pageSize: 50,
  pageNo: 1,
  offset: 0,
};

export type QueryParams = typeof DEFAULTS & {
  pageSize?: number;
  pageNo?: number;
  offset?: number;
  hostId?: string;
  supplierId?: string;
  createdAt?: { [key: string]: string };
  updatedAt?: { [key: string]: string };
};

const dateQueryValidationSchema = Joi.object({
  gte: Joi.date().optional(),
  gt: Joi.date().optional(),
  lte: Joi.date().optional(),
  lt: Joi.date().optional(),
  eq: Joi.date().optional(),
}).options({ stripUnknown: true });

const queryValidationSchema = Joi.object({
  supplierId: Joi.string().trim().guid().required(),
  search: Joi.string().trim(),
  sortBy: Joi.object(),
  createdAt: dateQueryValidationSchema,
  updatedAt: dateQueryValidationSchema,
  pageNo: Joi.number().min(1).positive().integer(),
  pageSize: Joi.number().positive().integer().min(1).max(1000),
}).options({ stripUnknown: true });

export default async function (auth: Authorization, query: QueryParams) {
  let value;
  try {
    value = await queryValidationSchema.validateAsync(query);
  } catch (error) {
    throw new BadRequest(error.message);
  }

  const hostId = value.supplierId;

  hasPromotionsReadPrivilege(auth, hostId);

  const combinedQuery = {
    ...DEFAULTS,
    ...value,
    hostId,
    supplierId: undefined,
  };

  const { pageSize, pageNo } = combinedQuery;

  const sequelizeQuery = buildQuery(combinedQuery);

  const promotions = await Promotion.findAll(sequelizeQuery);

  const totalResults = await Promotion.count({
    where: sequelizeQuery.where,
  });

  const data = PromotionViewModel.build(promotions);

  const results = {
    baseUrl: '/v1/promotions',
    data: data.toJSON(),
    totalResults,
    pageSize,
    query: {
      pageNo,
      pageSize,
    },
    pageNo,
  };

  const paginatedData = buildPagination(results).paginate();

  return paginatedData;
}
