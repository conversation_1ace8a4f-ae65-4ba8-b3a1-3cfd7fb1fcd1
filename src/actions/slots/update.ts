import httpErrors from 'http-errors';
import models from '../../models';
import validator from '../../validations/slots';

const update = async (body, id) => {
  const slot = await models.Slot.findByPk(id);
  if (!slot) {
    throw new httpErrors.NotFound();
  }

  try {
    const updated = await validator(body, slot);
    return await updated.save();
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default update;
