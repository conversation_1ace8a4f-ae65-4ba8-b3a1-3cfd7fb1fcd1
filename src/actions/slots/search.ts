import buildPagination from '@ordermentum/fireflight';
import models from '../../models';
import SlotViewModel from '../../view_models/slot';
import buildQuery from '../../lib/sequelize';

const DEFAULTS = {
  pageSize: 50,
  pageNo: 1,
  offset: 0,
};

const search = async raw => {
  const pageSize = raw.pageSize || DEFAULTS.pageSize;
  const pageNo = raw.pageNo || DEFAULTS.pageNo;

  const query = buildQuery(raw);

  const slots = await models.Slot.findAll(query);

  const totalResults = await models.Slot.count();

  const data = SlotViewModel.build(slots);

  const results = {
    baseUrl: '/v1/slots',
    data: data.toJSON(),
    totalResults,
    pageSize,
    query: {
      pageNo,
      pageSize,
    },
    pageNo,
  };

  const paginated = buildPagination(results).paginate();
  return paginated;
};

export default search;
