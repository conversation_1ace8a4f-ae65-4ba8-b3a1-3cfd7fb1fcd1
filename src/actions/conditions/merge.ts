import httpErrors from 'http-errors';
import { Condition, Audience } from '../../models';

export type MergeUserPayload = { userId: string };

export const mergeUserAction = async (id: string, data: MergeUserPayload) => {
  const condition = await Condition.findByPk(id);

  if (!condition) {
    throw new httpErrors.NotFound();
  }

  const rval = new Set([...condition.rval, data.userId]);
  await condition.update({ rval: Array.from(rval) });
  return condition;
};
export default mergeUserAction;
