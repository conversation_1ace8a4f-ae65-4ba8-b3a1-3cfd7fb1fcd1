import httpErrors from 'http-errors';
import models from '../../models';
import { updateValidator } from '../../validations/conditions';

const update = async (body, id) => {
  const condition = await models.Condition.findByPk(id);
  if (!condition) {
    throw new httpErrors.NotFound();
  }

  try {
    const updated = await updateValidator(body, condition);
    return await updated.save();
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default update;
