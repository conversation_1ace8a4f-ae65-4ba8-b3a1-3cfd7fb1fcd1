import httpErrors from 'http-errors';
import models from '../../models';
import ConditionViewModel from '../../view_models/condition';

const read = async id => {
  const condition = await models.Condition.findOne({
    include: [{ model: models.Audience, as: 'audience' }],
    where: {
      id,
    },
  });

  if (!condition) {
    throw new httpErrors.NotFound();
  }
  return ConditionViewModel.build(condition);
};

export default read;
