import httpErrors from 'http-errors';
import models from '../../models';
import FlagViewModel from '../../view_models/flag';

const read = async id => {
  const flag = await models.Flag.findOne({
    include: [
      {
        model: models.Audience,
        as: 'audiences',
        include: [{ model: models.Condition, as: 'conditions' }],
      },
    ],
    where: {
      id,
    },
  });
  if (!flag) {
    throw new httpErrors.NotFound();
  }
  return FlagViewModel.build(flag);
};

export default read;
