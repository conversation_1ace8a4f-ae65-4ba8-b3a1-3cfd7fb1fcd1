import buildPagination from '@ordermentum/fireflight';
import models from '../../models';
import FlagViewModel from '../../view_models/flag';
import buildQuery from '../../lib/sequelize';

const DEFAULTS = {
  pageSize: 50,
  pageNo: 1,
  offset: 0,
};

const search = async query => {
  const pageSize = query.pageSize || DEFAULTS.pageSize;
  const pageNo = query.pageNo || DEFAULTS.pageNo;
  const flags = await models.Flag.findAll(buildQuery(query));
  const totalResults = await models.Flag.count();

  const data = FlagViewModel.build(flags);

  const results = {
    baseUrl: '/v1/flags',
    data: data.toJSON(),
    totalResults,
    pageSize,
    query: {
      pageNo,
      pageSize,
    },
    pageNo,
  };

  const paginated = buildPagination(results).paginate();
  return paginated;
};

export default search;
