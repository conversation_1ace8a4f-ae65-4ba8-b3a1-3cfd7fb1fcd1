import httpErrors from 'http-errors';
import { Flag, Condition, Audience, FlagAudience } from '../../models';

export type MergeUserPayload = { userId: string };

export const mergeUserAction = async (id: string, data: MergeUserPayload) => {
  const flag = await Flag.findByPk(id);

  if (!flag) {
    throw new httpErrors.NotFound();
  }

  const [audience, created] = await Audience.findOrCreate({
    where: {
      name: `${flag.name} - users enabled`,
    },
  });

  if (created) {
    await audience.save();
  }

  await FlagAudience.findOrCreate({
    where: { flagId: flag.id, audienceId: audience.id },
  });

  const condition = await Condition.findOne({
    where: {
      key: 'userId',
      operator: 'includes',
      audienceId: audience.id,
    },
  });

  if (!condition) {
    await Condition.create({
      rval: [data.userId],
      key: 'userId',
      operator: 'includes',
      audienceId: audience.id,
    });
  } else {
    const rval = new Set([...condition.rval, data.userId]);
    await condition.update({ rval: Array.from(rval) });
  }
};
export default mergeUserAction;
