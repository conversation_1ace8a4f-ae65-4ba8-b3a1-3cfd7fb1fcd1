import { difference, get } from 'lodash';

import crypto from 'crypto';
import moment from 'moment';
import cache from '../../config/cache';
import { logger } from '../../config';
import { Audience, Flag, Condition } from '../../models';
import { FlagInstance } from '../../models/flag';
import { isInATargetAudience } from '../../lib';

export const isExpired = (flag: FlagInstance) => {
  if (!flag.expiresAt) {
    return false;
  }

  return moment().isAfter(flag.expiresAt);
};

export const getSignature = query =>
  crypto
    .createHmac('sha256', 'FLAGS')
    .update(JSON.stringify(query))
    .digest('base64');

export const getRollout = async (flag: FlagInstance, userId?: string) => {
  if (!userId) {
    return true;
  }
  // first check if the user has already been sampled into a weighted group
  // otherwise add them to a group
  const userEnabled = await cache.get(`${userId}:samples`, {});
  const seenUser = flag.name in userEnabled;
  if (!seenUser) {
    const point = Math.round(Math.random() * 100);
    userEnabled[flag.name] = point < flag.percentage ? 'IN' : 'OUT';
  }
  await cache.set(`${userId}:samples`, userEnabled);
  const enabled = userEnabled[flag.name] === 'IN';
  return enabled;
};

export const getFlags = async query => {
  const { userContext = {} } = query;
  const signature = getSignature(query);
  const cached = query.debug
    ? null
    : await cache.get(`flags:${signature}`, null);

  if (cached !== null) {
    return cached;
  }

  const namesToCheck = Array.isArray(query.flags) ? query.flags : [query.flags];

  const flags = await Flag.findAll({
    where: {
      name: namesToCheck,
    },
    include: [
      {
        model: Audience,
        as: 'audiences',
        include: [{ model: Condition, as: 'conditions' }],
      },
    ],
  });

  if (!flags.length) {
    return namesToCheck.reduce((p, flag) => ({ ...p, [flag]: false }), {});
  }

  const newCache = {};

  let profile = {};
  const userId = get(query, ['context', 'userId']);
  if (userId) {
    profile = await cache.get(`profile:${userId}`, {});
  }

  const context = { ...userContext, ...query.context, profile };

  for (const flag of flags) {
    const enabled =
      flag.alwaysEnabled ||
      isExpired(flag) ||
      isInATargetAudience(flag.audiences, context);

    if (enabled && flag.percentage < 100) {
      newCache[flag.name] = await getRollout(flag, userId);
    } else {
      newCache[flag.name] = enabled;
    }
  }

  logger.trace('result', newCache);

  // Set asked for flags that no longer exist to false
  const invalidFlags: string[] = [];
  for (const name of namesToCheck) {
    if (newCache[name] === undefined) {
      newCache[name] = false;
      invalidFlags.push(name);
    }
  }

  logger.trace('invalidflags', invalidFlags);

  await cache.set(`flags:${signature}`, newCache, 60);
  return newCache;
};
