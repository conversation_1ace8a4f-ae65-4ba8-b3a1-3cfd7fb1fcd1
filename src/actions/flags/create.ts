import httpErrors from 'http-errors';
import moment from 'moment';
import models from '../../models';
import validator from '../../validations/flags';

const create = async body => {
  try {
    const created = await validator(body, models.Flag.build());
    created.expiresAt = moment().add(90, 'days').toISOString();
    return await created.save();
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default create;
