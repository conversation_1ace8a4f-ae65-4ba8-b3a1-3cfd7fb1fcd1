import httpErrors from 'http-errors';
import models from '../../models';
import validator from '../../validations/flags';

const update = async (body, id) => {
  const flag = await models.Flag.findByPk(id);
  if (!flag) {
    throw new httpErrors.NotFound();
  }

  try {
    const updated = await validator(body, flag);
    return await updated.save();
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default update;
