import httpErrors from 'http-errors';
import { Experiment } from '../../models';
import validator from '../../validations/experiments';
import { updateExperimentAudiences } from './helper';
import read from './read';

const create = async body => {
  try {
    const experiment = Experiment.build();

    const validated = await validator(body, experiment);

    await validated.save();

    await updateExperimentAudiences({
      instance: experiment,
      newAudienceIds: validated.audienceIds,
    });

    return read(validated.id);
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default create;
