import httpErrors from 'http-errors';
import models from '../../models';

const read = async id => {
  const experiment = await models.Experiment.findOne({
    where: {
      id,
    },
    include: [
      {
        model: models.Slot,
        as: 'slot',
      },
      {
        model: models.Audience,
        as: 'audiences',
        include: [{ model: models.Condition, as: 'conditions' }],
      },
    ],
  });

  if (!experiment) {
    throw new httpErrors.NotFound();
  }
  return experiment;
};

export default read;
