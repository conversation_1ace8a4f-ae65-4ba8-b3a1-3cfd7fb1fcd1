import buildPagination from '@ordermentum/fireflight';
import models from '../../models';
import ExperimentViewModel from '../../view_models/experiment';
import buildQuery from '../../lib/sequelize';

const DEFAULTS = {
  pageSize: 50,
  pageNo: 1,
  offset: 0,
};

const search = async raw => {
  const pageSize = raw.pageSize || DEFAULTS.pageSize;
  const pageNo = raw.pageNo || DEFAULTS.pageNo;

  const query = buildQuery(raw);

  const experiments = await models.Experiment.findAll(query);

  const totalResults = await models.Experiment.count();

  const data = ExperimentViewModel.build(experiments);

  const results = {
    baseUrl: '/v1/experiments',
    data: data.toJSON(),
    totalResults,
    pageSize,
    query: {
      pageNo,
      pageSize,
    },
    pageNo,
  };

  const paginated = buildPagination(results).paginate();
  return paginated;
};

export default search;
