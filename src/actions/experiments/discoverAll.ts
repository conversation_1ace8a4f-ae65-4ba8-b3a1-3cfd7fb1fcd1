import moment from 'moment';
import useragent from 'useragent';
import _ from 'lodash';
import { Slot } from '../../models';
import DiscoverViewModel from '../../view_models/discover';
import { logger } from '../../config';
import { fetchExperiments, hydrate, getValidExperiments } from './helper';
import { ExperimentInstance } from '../../models/experiment';
import { parseDeviceType } from './device_type';
import cache from '../../config/cache';

type queryT = {
  [key: string]: string | string[];
};

export const discoverAll = async (
  userAgent: string = '',
  context: Record<string, any>,
  query: queryT
) => {
  const agent = useragent.parse(userAgent);
  const log = logger.child({ action: 'discoverAll' });

  const { slotNames = [] } = query;

  if (!slotNames.length) {
    log.error('no slot parameter found', { query });
    return [];
  }

  const slots = await Slot.findAll({
    where: {
      name: slotNames,
    },
  });

  const model = query.model || '';
  const deviceType =
    context.deviceType || query.deviceType || parseDeviceType(model);
  let profile = {};
  if (query.userId) {
    profile = await cache.get(`profile:${query.userId}`, {});
  }
  const parameters = {
    ...query,
    ...context,
    device: agent.device.family,
    os: agent.os.family,
    browser: agent.family,
    deviceType,
    profile,
  };

  if (!slots.length) {
    log.trace('no slots found for query', { parameters });
    return [];
  }

  const experiments: Array<ExperimentInstance> = [];
  for (const slot of slots) {
    /* eslint-disable no-await-in-loop */
    const data = await fetchExperiments({
      where: { slotId: slot.id },
      startAt: moment().toISOString(),
    });
    experiments.push(...data);
  }

  if (experiments.length === 0) {
    log.trace('no experiments found for slot', {
      parameters,
      query,
      slots,
    });
    return [];
  }

  const currentExperiments = await getValidExperiments({
    parameters,
    experiments,
  });

  if (experiments.length === 0) {
    log.trace('no valid audiences for experiments', {
      experiments,
      parameters,
      query,
    });
    return [];
  }

  return DiscoverViewModel.build(hydrate(currentExperiments, query)).toJSON();
};

export default discoverAll;
