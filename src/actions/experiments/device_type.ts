export const DEVICE_MAP = Object.freeze({
  i386: 'Simulator',
  x86_64: 'Simulator',
  'iPhone1,1': 'iPhone',
  'iPhone1,2': 'iPhone',
  'iPhone2,1': 'iPhone',
  'iPhone3,1': 'iPhone',
  'iPhone3,3': 'iPhone',
  'iPhone4,1': 'iPhone',
  'iPhone5,1': 'iPhone',
  'iPhone5,2': 'iPhone',
  'iPhone5,3': 'iPhone',
  'iPhone5,4': 'iPhone',
  'iPhone6,1': 'iPhone',
  'iPhone6,2': 'iPhone',
  'iPhone7,1': 'iPhone',
  'iPhone7,2': 'iPhone',
  'iPhone8,1': 'iPhone',
  'iPhone8,2': 'iPhone',
  'iPhone8,4': 'iPhone',
  'iPhone9,1': 'iPhone',
  'iPhone9,3': 'iPhone',
  'iPhone9,2': 'iPhone',
  'iPhone9,4': 'iPhone',
  'iPhone10,1': 'iPhone',
  'iPhone10,4': 'iPhone',
  'iPhone10,2': 'iPhone',
  'iPhone10,5': 'iPhone',
  'iPhone10,3': 'iPhone',
  'iPhone10,6': 'iPhone',
  'iPhone11,2': 'iPhone',
  'iPhone11,4': 'iPhone',
  'iPhone11,6': 'iPhone',
  'iPhone11,8': 'iPhone',
  'iPhone12,1': 'iPhone',
  'iPhone12,3': 'iPhone',
  'iPhone12,5': 'iPhone',
  'iPad1,1': 'iPad',
  'iPad2,1': 'iPad',
  'iPad2,2': 'iPad',
  'iPad2,3': 'iPad',
  'iPad2,4': 'iPad',
  'iPad2,5': 'iPad',
  'iPad2,6': 'iPad',
  'iPad2,7': 'iPad',
  'iPad3,1': 'iPad',
  'iPad3,2': 'iPad',
  'iPad3,3': 'iPad',
  'iPad3,4': 'iPad',
  'iPad3,5': 'iPad',
  'iPad3,6': 'iPad',
  'iPad4,1': 'iPad',
  'iPad4,2': 'iPad',
  'iPad4,3': 'iPad',
  'iPad4,4': 'iPad',
  'iPad4,5': 'iPad',
  'iPad4,6': 'iPad',
  'iPad4,7': 'iPad',
  'iPad4,8': 'iPad',
  'iPad4,9': 'iPad',
  'iPad5,1': 'iPad',
  'iPad5,2': 'iPad',
  'iPad5,3': 'iPad',
  'iPad5,4': 'iPad',
  'iPad6,3': 'iPad',
  'iPad6,4': 'iPad',
  'iPad6,7': 'iPad',
  'iPad6,8': 'iPad',
  'iPad6,11': 'iPad',
  'iPad6,12': 'iPad',
  'iPad7,1': 'iPad',
  'iPad7,2': 'iPad',
  'iPad7,3': 'iPad',
  'iPad7,4': 'iPad',
  'iPod1,1': 'iPod',
  'iPod2,1': 'iPod',
  'iPod3,1': 'iPod',
  'iPod4,1': 'iPod',
  'iPod7,1': 'iPod',
  'Watch1,1': 'Watch',
  'Watch1,2': 'Watch',
  'Watch2,6': 'Watch',
  'Watch2,7': 'Watch',
  'Watch2,3': 'Watch',
  'Watch2,4': 'Watch',
  'Watch3,1': 'Watch',
  'Watch3,2': 'Watch',
  'Watch3,3': 'Watch',
  'Watch3,4': 'Watch',
  'Watch4,1': 'Watch',
  'Watch4,2': 'Watch',
  'Watch4,3': 'Watch',
  'Watch4,4': 'Watch',
});

export const parseDeviceType = (model: string | string[]) => {
  if (Array.isArray(model)) {
    return 'Other';
  }

  if (model in DEVICE_MAP) {
    return DEVICE_MAP[model];
  }
  return 'Other';
};
