import httpErrors from 'http-errors';
import { get } from 'lodash';
import { incrementExperimentUser } from '../interactions';
import analytics from '../../lib/analytics';
import { Experiment } from '../../models';

type bodyT = {
  [key: string]: string;
};

const dismiss = async (body: bodyT = {}) => {
  if (!body.userId || !body.experimentId) {
    throw new httpErrors.NotFound();
  }

  const incrementDismissal = await incrementExperimentUser('dismissals')(
    body.experimentId,
    body.userId
  );

  const experiment = await Experiment.findOne({
    where: {
      id: body.experimentId,
    },
    attributes: ['name', 'slotId'],
  });

  analytics.track({
    userId: body.userId,
    event: 'EXPERIMENT_DISMISSED',
    properties: {
      experimentId: body.experimentId,
      experimentName: get(experiment, 'name', ''),
      slotId: get(experiment, 'slotId', ''),
    },
  });

  if (incrementDismissal) {
    return body.experimentId;
  }

  return '';
};

export default dismiss;
