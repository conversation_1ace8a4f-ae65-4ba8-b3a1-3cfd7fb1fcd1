import { Op } from 'sequelize';
import _ from 'lodash';
import models from '../../models';
import { getExperimentUser } from '../interactions';
import { ExperimentInstance } from '../../models/experiment';
import { isInATargetAudience } from '../../lib';

export const updateExperimentAudiences = async ({
  instance,
  newAudienceIds = [],
}) => {
  const existingAudienceIds = _.get(instance, 'audiences', []).map(a => a.id);

  const toDelete = _.difference(existingAudienceIds, newAudienceIds);
  const toCreate = _.difference(newAudienceIds, existingAudienceIds);

  const deleted = await models.ExperimentAudience.destroy({
    where: {
      experimentId: instance.id,
      audienceId: toDelete,
    },
  });

  const rows = toCreate.map(audienceId => ({
    experimentId: instance.id,
    audienceId,
  }));
  const created = await models.ExperimentAudience.bulkCreate(rows);

  return {
    deleted,
    created: created.length,
  };
};

export const showExperiment = (impressions, limit) => {
  if (!limit || limit === 0) return true;
  if (!impressions) return true;
  // we are using "<" (instead of "<=") because the first impression is null
  if (impressions < limit) return true;
  return false;
};

export const getCurrentExperiments = async (
  experiments: ExperimentInstance[],
  query
) => {
  const { userId } = query;
  const result = await Promise.all(
    experiments.map(async experiment => {
      const dismissal = await getExperimentUser('dismissals')(
        experiment.id,
        userId
      );
      if (dismissal) {
        return null;
      }

      const impressions = await getExperimentUser('impressions')(
        experiment.id,
        userId
      );
      const show = showExperiment(impressions, experiment.limit);
      if (show) {
        return experiment;
      }

      return null;
    })
  );
  const filtered = result.filter(x => x);
  return filtered;
};

type queryT = {
  [key: string]: string | Array<string> | object;
};
export const hydrate = (experiments, query: queryT) =>
  experiments.map(experiment =>
    Object.assign(experiment.get({ plain: true }), { query })
  );

export const fetchExperiments = async ({ where, startAt }) =>
  models.Experiment.findAll({
    where: {
      ...where,
      active: true,
      startAt: {
        [Op.lte]: startAt,
      },
    },
    order: [['priority', 'ASC']],
    include: [
      {
        model: models.Slot,
        as: 'slot',
      },
      {
        model: models.Audience,
        as: 'audiences',
        include: [
          {
            model: models.Condition,
            as: 'conditions',
          },
        ],
      },
    ],
  });

export const getValidExperiments = async ({
  parameters,
  experiments,
}: {
  parameters: queryT;
  experiments: Array<ExperimentInstance>;
}) => {
  const targetExperiments = experiments.filter(experiment =>
    isInATargetAudience(experiment.audiences, parameters)
  );

  if (targetExperiments.length === 0) {
    return [];
  }

  return getCurrentExperiments(targetExperiments, parameters);
};
