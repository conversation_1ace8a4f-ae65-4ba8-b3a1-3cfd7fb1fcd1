import moment from 'moment';
import useragent from 'useragent';
import _ from 'lodash';
import models from '../../models';
import DiscoverViewModel from '../../view_models/discover';
import { logger } from '../../config';
import { fetchExperiments, hydrate, getValidExperiments } from './helper';
import { incrementMultipleExperimentsUserImpressions } from '../interactions';
import { parseDeviceType } from './device_type';
import cache from '../../config/cache';

export type queryT = {
  [key: string]: string | Array<string>;
};
export const discover = async (
  userAgent: string = '',
  context: Record<string, any>,
  query: queryT = {}
) => {
  const agent = useragent.parse(userAgent);
  const log = logger.child({ action: 'discover' });

  if (!query.slot) {
    log.error('no slot parameter found', { query });
    return [];
  }

  const slot = await models.Slot.findOne({
    where: {
      name: query.slot,
    },
  });

  const model = query.model || '';
  const deviceType =
    context.deviceType || query.deviceType || parseDeviceType(model);
  const { operatingSystem } = context;
  let profile = {};
  if (query.userId) {
    profile = await cache.get(`profile:${query.userId}`, {});
  }

  const parameters = {
    ...query,
    ...context,
    device: agent.device.family,
    os: agent.os.family,
    browser: agent.family,
    deviceType,
    profile,
    operatingSystem,
  };

  if (!slot) {
    log.trace('no slot found for query', { parameters });
    return [];
  }

  const experiments = await fetchExperiments({
    where: { slotId: slot.id },
    startAt: moment().toISOString(),
  });

  if (experiments.length === 0) {
    log.trace('no experiments found for slot', {
      parameters,
      query,
      slot: slot.name,
    });
    return [];
  }
  const currentExperiments = await getValidExperiments({
    parameters,
    experiments,
  });

  if (!currentExperiments.length) {
    log.trace('no valid audiences for experiments', {
      experiments,
      parameters,
      query,
    });
    return [];
  }
  incrementMultipleExperimentsUserImpressions(currentExperiments, parameters);
  const view = DiscoverViewModel.build(
    hydrate(currentExperiments, query)
  ).toJSON();

  return view;
};

export default discover;
