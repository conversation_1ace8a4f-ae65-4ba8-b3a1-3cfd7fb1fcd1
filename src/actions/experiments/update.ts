import _ from 'lodash';
import httpErrors from 'http-errors';
import validator from '../../validations/experiments';
import read from './read';
import { updateExperimentAudiences } from './helper';

const update = async (body, id) => {
  try {
    const experiment = await read(id);

    if (!experiment) {
      throw new httpErrors.NotFound();
    }
    const validated = await validator(body, experiment);

    await validated.save();

    await updateExperimentAudiences({
      instance: experiment,
      newAudienceIds: validated.audienceIds,
    });

    return read(validated.id);
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default update;
