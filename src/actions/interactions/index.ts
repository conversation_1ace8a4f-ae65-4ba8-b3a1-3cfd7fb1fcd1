import { get } from 'lodash';
import analytics from '../../lib/analytics';
import redis from '../../config/redis';

type interactionT = 'impressions' | 'dismissals' | 'acceptances';
type groupT = 'users';

const zsetKey = (experimentId, interaction: interactionT, group: groupT) =>
  `experiments:${experimentId}:${interaction}:${group}`;

const zsetKeyUser = interaction => experimentId =>
  zsetKey(experimentId, interaction, 'users');

export const incrementExperimentUser = (interaction: interactionT) => async (
  experimentId,
  userId
) => redis.zincrby(zsetKeyUser(interaction)(experimentId), 1, userId);

export const getExperimentUser = (interaction: interactionT) => (
  experimentId,
  userId
) => redis.zscore(zsetKeyUser(interaction)(experimentId), userId);

export const getExperimentAllUserImpressions = experimentId =>
  redis.zrevrangebyscore(
    zsetKeyUser('impressions')(experimentId),
    '+inf',
    '-inf',
    'WITHSCORES'
  );

export const incrementMultipleExperimentsUserImpressions = (
  experiments,
  query
) => {
  for (const experiment of experiments) {
    const { userId } = query;
    if (userId) {
      incrementExperimentUser('impressions')(experiment.id, userId);
      analytics.track({
        userId,
        event: 'EXPERIMENT_IMPRESSION',
        properties: {
          experimentId: experiment.id,
          experimentName: experiment.name,
          slotId: get(experiment, 'slot.id', ''),
          slotName: get(experiment, 'slot.name', ''),
          userId: query.userId,
          retailerId: query.retailerId,
          supplierId: query.supplierId,
          version: query.version,
          source: query.source,
          browser: query.browser,
          model: query.deviceType,
        },
      });
    }
  }
};
