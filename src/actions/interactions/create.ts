import httpErrors from 'http-errors';
import validator from '../../validations/interactions';
import { incrementExperimentUser } from './index';

const INCREMENTORS = {
  acceptance: incrementExperimentUser('acceptances'),
  dismissal: incrementExperimentUser('dismissals'),
  impression: incrementExperimentUser('impressions'),
};

export const create = async body => {
  try {
    const validated = await validator(body, {});

    const { type, experimentId, userId } = validated;

    return INCREMENTORS[type](experimentId, userId);
  } catch (ex) {
    throw new httpErrors.BadRequest(ex);
  }
};

export default create;
