import { ViewModel } from '@ordermentum/slingshot';

class PromotionViewModel extends ViewModel {
  toJSON() {
    const json = {
      id: this.id,
      name: this.name,
      hostId: this.hostId,
      description: this.description,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      conditions: this.conditions,
      effects: this.effects,
      sponsored: this.sponsored,
      startAt: this.startAt,
      finishAt: this.finishAt,
      coupon: this.coupon,
      usage: this.usage,
    };
    return json;
  }

  toEventModel() {
    return {
      id: this.id,
      name: this.name,
      coupon: this.coupon,
      description: this.description,
      hostId: this.hostId,
      sponsored: this.sponsored,
      conditions: this.conditions,
      effects: this.effects,
      startAt: this.startAt,
      finishAt: this.finishAt,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      usage: this.usage,
    };
  }
}

PromotionViewModel.delegates = [
  'id',
  'name',
  'description',
  'conditions',
  'effects',
  'sponsored',
  'createdAt',
  'updatedAt',
  'hostId',
  'startAt',
  'finishAt',
  'coupon',
  'usage',
];

export default PromotionViewModel;
