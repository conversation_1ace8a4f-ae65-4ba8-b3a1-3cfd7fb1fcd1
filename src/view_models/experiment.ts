import { ViewModel } from '@ordermentum/slingshot';
import AudienceViewModel from './audience';

const nullSlot = {
  name: '',
};

class ExperimentViewModel extends ViewModel {
  get audiences() {
    return this.model.audiences
      ? AudienceViewModel.build(this.model.audiences).toJSON()
      : [];
  }

  get audienceIds() {
    return this.audiences.map(a => a.id);
  }

  get slot() {
    return this.model.slot || nullSlot;
  }

  toJSON() {
    const json = {
      id: this.id,
      name: this.name,
      meta: this.meta,
      priority: this.priority,
      active: this.active,
      exclusive: this.exclusive,

      limit: this.limit,
      interval: this.interval,

      audiences: this.audiences,
      audienceIds: this.audienceIds,
      slotId: this.slotId,
      slot: this.slot,

      startAt: this.startAt,
      endAt: this.endAt,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      slotName: this.slot?.name,
    };
    return json;
  }
}

ExperimentViewModel.delegates = [
  'id',
  'name',
  'meta',
  'priority',
  'active',
  'exclusive',

  'slotId',

  'limit',
  'interval',
  'startAt',
  'endAt',

  'createdAt',
  'updatedAt',
];

export default ExperimentViewModel;
