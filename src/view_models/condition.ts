import { ViewModel } from '@ordermentum/slingshot';

class ConditionViewModel extends ViewModel {
  toJSON() {
    const json = {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      audienceId: this.audienceId,
      key: this.key,
      operator: this.operator,
      rval: this.rval,
    };
    return json;
  }
}

ConditionViewModel.delegates = [
  'id',
  'audienceId',
  'key',
  'rval',
  'operator',
  'createdAt',
  'updatedAt',
];

export default ConditionViewModel;
