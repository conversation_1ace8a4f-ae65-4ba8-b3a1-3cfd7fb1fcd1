import { ViewModel } from '@ordermentum/slingshot';

class PromotionsUsageViewModel extends ViewModel {
  toJSON() {
    const json = {
      id: this.id,
      supplierId: this.supplierId,
      promotionId: this.promotionId,
      retailerId: this.retailerId,
      purchaserId: this.purchaserId,
      userId: this.userId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
    };
    return json;
  }

  toEventModel() {
    return {
      id: this.id,
      supplierId: this.supplierId,
      promotionId: this.promotionId,
      retailerId: this.retailerId,
      purchaserId: this.purchaserId,
      userId: this.userId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
    };
  }
}

PromotionsUsageViewModel.delegates = [
  'id',
  'promotionId',
  'supplierId',
  'retailerId',
  'userId',
  'createdAt',
  'deletedAt',
  'updatedAt',
];

export default PromotionsUsageViewModel;
