import { ViewModel } from '@ordermentum/slingshot';
import ConditionViewModel from './condition';

class AudienceViewModel extends ViewModel {
  get conditions() {
    return this.model.conditions
      ? ConditionViewModel.build(this.model.conditions)
      : [];
  }

  toJSON() {
    const json = {
      id: this.id,
      name: this.name,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      conditions: this.conditions,
    };
    return json;
  }
}

AudienceViewModel.delegates = [
  'id',
  'name',
  'createdAt',
  'updatedAt',
  'conditions',
];

export default AudienceViewModel;
