import { ViewModel } from '@ordermentum/slingshot';
import AudienceViewModel from './audience';

class FlagViewModel extends ViewModel {
  get audiences() {
    return this.model.audiences
      ? AudienceViewModel.build(this.model.audiences)
      : [];
  }

  toJSON() {
    const json = {
      id: this.id,
      name: this.name,
      alwaysEnabled: this.alwaysEnabled,
      expiresAt: this.expiresAt,
      percentage: this.percentage,
      audiences: this.audiences,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
    return json;
  }
}

FlagViewModel.delegates = [
  'id',
  'name',
  'alwaysEnabled',
  'expiresAt',
  'percentage',
  'createdAt',
  'updatedAt',
  'audiences',
];

export default FlagViewModel;
