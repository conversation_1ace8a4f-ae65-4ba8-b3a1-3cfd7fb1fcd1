import { ViewModel } from '@ordermentum/slingshot';
import { pick, get } from 'lodash';
import handlebars from 'handlebars';

const nullSlot = {
  name: '',
};

const PARAMETERS = [
  'supplierId',
  'retailerId',
  'purchaserId',
  'userId',
  'device',
  'os',
  'browser',
  'admin',
];

const generateLink = (template, context = {}) => {
  if (!template) return undefined;
  return handlebars.compile(template)(pick(context, PARAMETERS));
};

class DiscoverViewModel extends ViewModel {
  get slotModel() {
    return this.model.slot || nullSlot;
  }

  get meta() {
    return { ...this.model.meta, link: generateLink(this.link, this.query) };
  }

  get link() {
    return get(this.model, ['meta', 'link']);
  }

  get query() {
    return this.model.query || {};
  }

  get slot() {
    return this.slotModel.name;
  }

  toJSON() {
    const json = {
      id: this.id,
      slot: this.slot,
      name: this.name,
      meta: this.meta,
      slotName: this.slotModel?.name,
    };
    return json;
  }
}

DiscoverViewModel.delegates = ['id', 'name', 'meta'];

export default DiscoverViewModel;
