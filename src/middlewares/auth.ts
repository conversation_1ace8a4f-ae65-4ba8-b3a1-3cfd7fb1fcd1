import { Authorization } from '@ordermentum/auth-driver';
import createAuth from '@ordermentum/auth-middleware';
import type { RequestHandler } from 'express';

import * as env from '../config/env';

const hasAdminToken = req => req.token === env.SECRET_KEY;

const createAuthorization = async (req, res, next) => {
  if (req.locals?.asapClaims?.admin) {
    return next();
  }
  req.auth = new Authorization(req.user);
  return next();
};

const createAuthWithAsap = (req, res, next) => {
  if (req.locals?.asapClaims?.admin) {
    return next();
  }

  return createAuth({
    JWTSecret: env.JWT_SECRET,
    fetchUser: true,
    authURL: env.AUTH_URL,
  })(req, res, next);
};

export const validateAuthWithAsap = [createAuthWithAsap, createAuthorization];

const validateAuth: Array<RequestHandler> = [
  createAuth({
    JWTSecret: env.JWT_SECRET,
    customCheck: hasAdminToken,
    fetchUser: true,
    authURL: env.AUTH_URL,
  }),
  createAuthorization,
];

export default validateAuth;
