import type { Request<PERSON><PERSON><PERSON> } from 'express';
import { logger } from '../config';

const accessLog: RequestHandler = (req, res, next) => {
  res.on('finish', () => {
    logger.info(
      {
        remoteAddress: req.ip,
        method: req.method,
        url: req.originalUrl,
        protocol: req.protocol,
        hostname: req.hostname,
        body: req.body,
        query: req.query,
        params: req.params,
        userAgent: req.headers['user-agent'],
        status: res.statusCode,
      },
      'access_log'
    );
  });
  next();
};

export default accessLog;
