import { createAsapAuthenticationMiddleware } from '@ordermentum/express-asap';
import { Request, Response, NextFunction } from 'express';
import { getAdminAuth } from '@ordermentum/auth-driver';
import { ASAP_PUBLIC_KEYS_URL, NODE_ENV } from '../config/env';

export const ASAP_AUDIENCE = 'flags';

const insecureMode = ['development', 'test'].includes(NODE_ENV);

export default ASAP_PUBLIC_KEYS_URL || insecureMode
  ? (req: Request, res: Response, next: NextFunction) => {
      const middleware = createAsapAuthenticationMiddleware({
        resourceServerAudience: ASAP_AUDIENCE,
        maxLifeTimeSeconds: 60 * 5, // 5 minute expiry
        publicKeyBaseUrls: [ASAP_PUBLIC_KEYS_URL ?? ''],
        insecureMode,
      });

      return middleware(req, res, () => {
        if (req?.locals?.asapClaims?.admin && req.locals?.asapClaims?.userId) {
          req.auth = getAdminAuth(req.locals.asapClaims.userId);
          // @ts-expect-error
          req.user = {
            id: req.locals.asapClaims.userId,
          };
        }
        next();
      });
    }
  : (_req: Request, _res: Response, next: NextFunction) => next();
