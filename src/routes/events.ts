import { Router } from 'express';
import _ from 'lodash';
import cache from '../config/cache';

const router = Router();

router.post('/', async (req, res) => {
  const userId = _.get(req.body, 'userId');
  const traits = _.get(req.body, 'traits', {});
  if (userId) {
    const key = `profile:${userId}`;
    const existing = await cache.get(key, {});
    const profile = { ...existing, ...traits };
    await cache.set(key, profile, 31622400);
  }
  res.json({ status: 'ok' });
});

export default router;
