import { Router, Request } from 'express';
import joi from '@hapi/joi';
import { BadRequest } from 'http-errors';
import auth from '../middlewares/auth';
import searchAction from '../actions/flags/search';
import readAction from '../actions/flags/read';
import createAction from '../actions/flags/create';
import updateAction from '../actions/flags/update';
import deleteAction from '../actions/flags/delete';
import mergeUserAction, { MergeUserPayload } from '../actions/flags/merge';
import * as flagsCheck from '../actions/flags/check';

export const getUserContext = (req: Request) => {
  let context = {};
  const header = req.headers?.['user-context'];

  if (header && typeof header === 'string') {
    const buff = Buffer.from(header, 'base64');
    context = JSON.parse(buff.toString('utf-8'));
  }

  return context;
};

const router = Router();

router.get('/', auth, async (req, res) => {
  const results = await searchAction(req.query);
  res.json(results);
});

router.get('/check', async (req, res, next) => {
  const userContext = getUserContext(req);
  const resp = await flagsCheck.getFlags({ userContext, ...req.query });
  if (!req.query.debug) res.setHeader('Cache-Control', 'public, max-age=60');
  res.json(resp);
});

router.post('/check', async (req, res) => {
  const resp = await flagsCheck.getFlags(req.body);
  res.json(resp);
});

router.get('/:id', auth, async (req, res, next) => {
  const flag = await readAction(req.params.id);
  res.json(flag);
});

router.post('/', auth, async (req, res, next) => {
  const newFlag = await createAction(req.body);
  res.json(newFlag);
});

router.put('/:id', auth, async (req, res, next) => {
  await updateAction(req.body, req.params.id);
  const flag = await readAction(req.params.id);
  res.json(flag);
});

const mergeUserSchema = joi.object<MergeUserPayload>({
  userId: joi.string().guid().required(),
});

router.post('/:id/add-user', async (req, res) => {
  const {
    value,
    error,
  }: { error?: any; value: MergeUserPayload } = mergeUserSchema.validate(
    req.body,
    {
      allowUnknown: true,
    }
  );

  if (error) {
    throw new BadRequest(error);
  }

  await mergeUserAction(req.params.id, value);

  res.json({ status: 'ok' });
});

router.delete('/:id', auth, async (req, res, next) => {
  await deleteAction(req.params.id);
  res.status(204).json(null);
});

export default router;
