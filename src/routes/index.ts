import { Router } from 'express';

import flags from './flags';
import conditions from './conditions';
import audiences from './audiences';
import experiments from './experiments';
import events from './events';
import slots from './slots';
import profile from './profile';
import interactions from './interactions';
import promotions from './promotions';
import promotionsUsage from './promotions_usage';

const v1 = Router()
  .use('/flags', flags)
  .use('/conditions', conditions)
  .use('/experiments', experiments)
  .use('/slots', slots)
  .use('/events', events)
  .use('/audiences', audiences)
  .use('/profile', profile)
  .use('/interactions', interactions)
  .use('/promotions', promotions)
  .use('/promotions/usage', promotionsUsage);

const router = Router();

router.use('/v1', v1);

export default router;
