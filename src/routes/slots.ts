import { Router } from 'express';
import auth from '../middlewares/auth';

import searchAction from '../actions/slots/search';
import readAction from '../actions/slots/read';
import createAction from '../actions/slots/create';
import updateAction from '../actions/slots/update';
import deleteAction from '../actions/slots/delete';
import SlotViewModel from '../view_models/slot';

const router = Router();
router.use(auth);

router.get('/', async (req, res) => {
  const results = await searchAction(req.query);
  res.json(results);
});

router.get('/:id', async (req, res, next) => {
  const slot = await readAction(req.params.id);
  res.json(SlotViewModel.build(slot));
});

router.post('/', async (req, res, next) => {
  const slot = await createAction(req.body);
  res.json(SlotViewModel.build(slot));
});

router.put('/:id', async (req, res, next) => {
  const slot = await updateAction(req.body, req.params.id);
  res.json(SlotViewModel.build(slot));
});

router.delete('/:id', async (req, res, next) => {
  await deleteAction(req.params.id);
  res.status(204).json(null);
});

export default router;
