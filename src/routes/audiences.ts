import { Router } from 'express';
import auth from '../middlewares/auth';

import searchAction from '../actions/audiences/search';
import readAction from '../actions/audiences/read';
import createAction from '../actions/audiences/create';
import updateAction from '../actions/audiences/update';
import deleteAction from '../actions/audiences/delete';

const router = Router();

router.use(auth);

router.get('/', async (req, res) => {
  const results = await searchAction(req.query);
  res.json(results);
});

router.get('/:id', async (req, res, next) => {
  const audience = await readAction(req.params.id);
  res.json(audience);
});

router.post('/', async (req, res, next) => {
  const newRule = await createAction(req.body);
  res.json(newRule);
});

router.put('/:id', async (req, res, next) => {
  await updateAction(req.body, req.params.id);
  const audience = await readAction(req.params.id);
  res.json(audience);
});

router.delete('/:id', async (req, res, next) => {
  await deleteAction(req.params.id);
  res.status(204).json(null);
});

export default router;
