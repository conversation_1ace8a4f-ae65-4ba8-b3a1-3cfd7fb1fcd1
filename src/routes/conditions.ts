import { Router } from 'express';
import joi from '@hapi/joi';
import { BadRequest } from 'http-errors';
import auth from '../middlewares/auth';

import searchAction from '../actions/conditions/search';
import readAction from '../actions/conditions/read';
import createAction from '../actions/conditions/create';
import updateAction from '../actions/conditions/update';
import deleteAction from '../actions/conditions/delete';
import mergeUserAction, { MergeUserPayload } from '../actions/conditions/merge';

const router = Router();
router.use(auth);

router.get('/', async (req, res) => {
  const resp = await searchAction();
  res.json(resp);
});

router.get('/:id', async (req, res, next) => {
  const condition = await readAction(req.params.id);
  res.json(condition);
});

router.post('/', async (req, res, next) => {
  const newCondition = await createAction(req.body);
  res.json(newCondition);
});

router.put('/:id', async (req, res, next) => {
  await updateAction(req.body, req.params.id);
  const condition = await readAction(req.params.id);
  res.json(condition);
});

const mergeUserSchema = joi.object<MergeUserPayload>({
  userId: joi.string().guid().required(),
});

router.post('/:id/add-user', async (req, res) => {
  const {
    value,
    error,
  }: { error?: any; value: MergeUserPayload } = mergeUserSchema.validate(
    req.body,
    {
      allowUnknown: true,
    }
  );

  if (error) {
    throw new BadRequest(error);
  }

  await mergeUserAction(req.params.id, value);

  res.json({ status: 'ok' });
});

router.delete('/:id', async (req, res, next) => {
  await deleteAction(req.params.id);
  res.status(204).json(null);
});

export default router;
