import { Router } from 'express';
import _ from 'lodash';
import auth from '../middlewares/auth';
import cache from '../config/cache';

const router = Router();

router.get('/:id', auth, async (req, res) => {
  const userId = req.params?.id;

  if (!userId) {
    return res.json({});
  }

  const key = `profile:${userId}`;
  const profile = await cache.get(key);
  return res.json(profile);
});

export default router;
