import { Router, Request } from 'express';
import { get } from 'lodash';
import auth from '../middlewares/auth';
import discoverAction, { queryT } from '../actions/experiments/discover';
import searchAction from '../actions/experiments/search';
import readAction from '../actions/experiments/read';
import createAction from '../actions/experiments/create';
import updateAction from '../actions/experiments/update';
import deleteAction from '../actions/experiments/delete';

import discoverAllAction from '../actions/experiments/discoverAll';
import dismissAction from '../actions/experiments/dismiss';
import ExperimentViewModel from '../view_models/experiment';

import { logger } from '../config';
import { getUserContext } from './flags';

const router = Router();

router.get('/', async (req, res) => {
  const results = await searchAction(req.query);
  res.json(results);
});

router.get<{}, any, any, queryT>('/discover', async (req, res) => {
  const userAgent = req.headers['user-agent'] || '';
  const userContext = getUserContext(req);
  const results = await discoverAction(userAgent, userContext, req.query);

  logger.info('Results', { results });

  res.json({
    data: results,
  });
});

router.get<{}, any, any, queryT>('/discover-all', async (req, res) => {
  const userAgent = req.headers['user-agent'] || '';
  const userContext = getUserContext(req);
  const results = await discoverAllAction(userAgent, userContext, req.query);

  logger.info('Results', results);

  res.json({
    data: results,
  });
});

router.get('/:id', auth, async (req, res, next) => {
  const experiment = await readAction(req.params.id);
  res.json(ExperimentViewModel.build(experiment));
});

router.post('/', auth, async (req, res, next) => {
  const experiment = await createAction(req.body);
  res.json(ExperimentViewModel.build(experiment));
});

router.post('/dismiss', async (req, res, next) => {
  const id = await dismissAction(req.body);
  res.json({
    data: id,
  });
});

router.put('/:id', auth, async (req, res, next) => {
  const experiment = await updateAction(req.body, req.params.id);
  res.json(ExperimentViewModel.build(experiment));
});

router.delete('/:id', auth, async (req, res, next) => {
  await deleteAction(req.params.id);
  res.status(204).json(null);
});

export default router;
