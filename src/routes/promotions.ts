import { Router, Request, Response } from 'express';
import <PERSON><PERSON> from '@hapi/joi';
import { BadRequest } from 'http-errors';
import { QueryParams } from '../actions/promotions/search';
import auth, { validateAuthWithAsap } from '../middlewares/auth';
import {
  check,
  search,
  read,
  create,
  update,
  destroy,
} from '../actions/promotions';
import asapMiddleware from '../middlewares/asap';

const router = Router();

const deleteValidator = Joi.string().trim().guid().required();

router.get<{}, any, any, QueryParams>('/', auth, async (req, res) => {
  const results = await search(req.auth, req.query);
  res.json(results);
});

router.post('/check', async (req: Request, res: Response) => {
  const resp = await check(req.body);
  res.json(resp);
});

router.get('/:id', auth, async (req: Request, res: Response) => {
  const { id } = req.params;
  const promotion = await read(req.auth, id);
  res.json(promotion);
});

const authWithAsap = [asapMiddleware, ...validateAuthWithAsap];

router.post('/', authWithAsap, async (req: Request, res: Response) => {
  const newPromotion = await create(req.auth, req.body);
  res.json(newPromotion);
});

router.put('/:id', auth, async (req: Request, res: Response) => {
  const { id } = req.params;
  const updatedPromotion = await update(req.auth, id, req.body);
  res.json(updatedPromotion);
});

router.delete('/:id', auth, async (req: Request, res: Response) => {
  const { value, error }: { error?; value } = deleteValidator.validate(
    req.params.id
  );
  if (error) {
    throw new BadRequest(error.message);
  }
  await destroy(req.auth, value);
  res.status(204).json(null);
});

export default router;
