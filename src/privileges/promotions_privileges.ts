import { Authorization } from '@ordermentum/auth-driver';
import { Forbidden } from 'http-errors';

/**
 * @description
 * @returns true if user has suppliers read access
 * @param auth {Authorization}
 * @param supplierId
 */
export const hasPromotionsReadPrivilege = (
  auth: Authorization,
  supplierId: string
) => {
  if (
    !auth.canOne(['promotions-full', 'promotions-read'], supplierId, 'supplier')
  ) {
    throw new Forbidden('No privilege');
  }
};

/**
 * @description
 * Throw if the user has no suppliers access
 * @param auth {Authorization}
 * @param supplierId
 */
export const hasPromotionsCreatePrivilege = (
  auth: Authorization,
  supplierId?: string
) => {
  if (!supplierId) {
    const isAdmin = auth.isSuperUser || auth.isAdmin;
    if (!isAdmin) {
      throw new Forbidden('User cannot create promotions without a hostId');
    }
  }

  if (
    !auth.canOne(
      ['promotions-full', 'promotions-create'],
      supplierId,
      'supplier'
    )
  ) {
    throw new Forbidden('No privilege');
  }
};

/**
 * @description
 * Throw if the user has no suppliers access
 * @param auth {Authorization}
 * @param supplierId
 */
export const hasPromotionsUpdatePrivilege = (
  auth: Authorization,
  supplierId: string
) => {
  if (
    !auth.canOne(
      ['promotions-full', 'promotions-update'],
      supplierId,
      'supplier'
    )
  ) {
    throw new Forbidden('No privilege');
  }
};

/**
 * @description
 * Throw if the user has no suppliers access
 * @param auth {Authorization}
 * @param supplierId
 */
export const hasPromotionsFullPrivilege = (
  auth: Authorization,
  supplierId: string
) => {
  if (!auth.can('promotions-full', supplierId, 'supplier')) {
    throw new Forbidden('No privilege');
  }
};
