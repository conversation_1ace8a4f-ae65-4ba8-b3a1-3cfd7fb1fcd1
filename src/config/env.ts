import { LogLevel } from 'bunyan';

require('dotenv-safe').load({ sample: './.env.example' });

export const DEPLOYED = ['testing', 'production'].includes(
  process.env.NODE_ENV
);

export const LOG_LEVEL = (process.env.LOG_LEVEL as LogLevel) || 'info';
export const {
  DATABASE_URI,
  NODE_ENV,
  PORT,
  REDIS_URL,
  JWT_SECRET,
  AUTH_URL,
  SECRET_KEY,
  SENTRY_DSN,
  STATSD_HOST,
  STATSD_PORT,
  ASAP_PUBLIC_KEYS_URL,
} = process.env;

export const SEGMENT_WRITE_KEY =
  process.env.SEGMENT_WRITE_KEY || 'A90sWHzAiBxFzuaS4oKwyU0gb855x3Cf';
