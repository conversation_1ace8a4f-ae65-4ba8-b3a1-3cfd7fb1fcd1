import Redis from 'ioredis';
import _ from 'lodash';
import logger from './logger';
import * as env from './env';

const redisOptions = {
  keepAlive: 10000,
} as Record<string, any>;

if (process.env.REDIS_USE_TLS === 'true') {
  redisOptions.tls = true;
}
if (process.env.REDIS_PASSWORD) {
  redisOptions.password = process.env.REDIS_PASSWORD;
}

const client = new Redis({
  host: env.REDIS_URL,
  keyPrefix: 'flags',
  ...redisOptions,
});

client.on('error', err => {
  logger.error('Error from redis client:', err);
  throw err;
});

process.on('exit', code => {
  logger.info('Flags service exiting with code:', code);
  client.quit();
});

export default client;
