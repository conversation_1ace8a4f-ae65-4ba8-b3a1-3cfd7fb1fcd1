import util from 'util';
import bunyan from 'bunyan';
import * as env from './env';

util.inspect.defaultOptions.breakLength = Infinity; // This is to avoid util format adding new line characters
util.inspect.defaultOptions.maxArrayLength = Infinity; // Do not truncate arrays

function serializeRequest(req) {
  return {
    url: req.originalUrl || req.url,
    reqId: req.reqId,
    method: req.method,
    query: req.query,
  };
}

function serializeResponse(res) {
  return {
    statusCode: res.statusCode,
  };
}

const logger = bunyan.createLogger({
  name: 'flags',
  environment: env.NODE_ENV,
  level: env.LOG_LEVEL as bunyan.LogLevel,
  streams: [
    {
      stream: process.stdout,
    },
  ],
  serializers: {
    err: bunyan.stdSerializers.err,
    req: serializeRequest,
    res: serializeResponse,
  },
});

export default logger;
