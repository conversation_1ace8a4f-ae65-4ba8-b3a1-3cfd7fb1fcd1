import express from 'express';

import bunyanMiddleware from 'bunyan-middleware';
import prowl from '@ordermentum/prowl';
import bodyParser from 'body-parser';
import bearerToken from 'express-bearer-token';
import httpContext from 'express-http-context';
import { setup } from 'asclepius-standard';
import 'express-async-errors';
import dogstatsd from 'node-dogstatsd';
import datadog from 'connect-datadog';
import * as env from './config/env';
import logger from './config/logger';
import Sentry from './config/sentry';
import { accessLog, errors } from './middlewares';
import routes from './routes';
import { sequelize } from './models';
import redis from './config/redis';
import setUserContext from './middlewares/user_context';

const app = express();

const domains = {
  testing: {
    wssHost: 'wss://*.ordermentum-sandbox.com/',
    host: 'https://*.ordermentum-sandbox.com/',
  },
  production: {
    wssHost: 'wss://*.ordermentum.com/',
    host: 'https://*.ordermentum.com/',
  },
};

const host = domains?.[env.NODE_ENV]?.host ?? 'http://localhost:*';
const wssHost = domains?.[env.NODE_ENV]?.wssHost ?? 'ws://localhost:*';

prowl(app, {
  host,
  wssHost,
  useCors: true,
});

const DATADOG_OPTIONS = {
  response_code: true,
  tags: ['app:flags', env.NODE_ENV],
  dogstatsd: new dogstatsd.StatsD(env.STATSD_HOST, Number(env.STATSD_PORT)),
};

const datadogMiddleware = datadog(DATADOG_OPTIONS);

app.use(datadogMiddleware);

app.use(bearerToken());

app.use(Sentry.Handlers.requestHandler());

app.use(accessLog);

app.use(
  bodyParser.urlencoded({
    extended: false,
    limit: '5mb',
  })
);

app.use(
  bunyanMiddleware({
    logger,
  })
);

app.use(
  bodyParser.json({
    limit: '5mb',
  })
);

app.use(httpContext.middleware);
app.use(setUserContext);

app.get('/health', (_req, res) => res.status(200).send('ok'));
app.get('/live', setup({ sequelize, redis, logger }));

app.use(routes);
app.use(Sentry.Handlers.errorHandler());
app.use(errors);
export default app;
