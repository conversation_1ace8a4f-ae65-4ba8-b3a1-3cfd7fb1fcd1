import _ from 'lodash';
import { lte, gte } from 'lodash/fp';
import { logger } from '../config';
import { compareDate, equals, includes } from './helper';

const operatorHandlers = {
  lte: compareDate(lte),
  gte: compareDate(gte),
  equals,
  includes,
};

const OPERATIONS = {
  gt: { operator: 'lte', negate: true },
  lt: { operator: 'gte', negate: true },
  lte: { operator: 'lte', negate: false },
  gte: { operator: 'gte', negate: false },
  includes: { operator: 'includes', negate: false },
  not_includes: { operator: 'includes', negate: true },
  equals: { operator: 'equals', negate: false },
  not_equals: { operator: 'equals', negate: true },
};

export const queryValueSatisfiesCondition = (condition, value): boolean => {
  const { key, rval } = condition;

  if (!value) {
    return false;
  }

  if (!OPERATIONS[condition.operator]) {
    return false;
  }

  const { operator, negate } = OPERATIONS[condition.operator];

  try {
    return operatorHandlers[operator](value, rval, negate);
  } catch (e) {
    logger.error('value does not satisfy condition', { condition, value });
    return false;
  }
};

export const allQueryValuesSuitable = (conditions, query) =>
  conditions.reduce((allSuitable, condition) => {
    if (!allSuitable) {
      return false;
    }
    const value = _.get(query, condition.key, null);

    return queryValueSatisfiesCondition(condition, value);
  }, true);

export const isInATargetAudience = (audiences, query): boolean =>
  audiences.reduce((isSuitableAudience, audience) => {
    if (isSuitableAudience) {
      return true;
    }
    if (audience.conditions.length === 0) {
      return false;
    }
    return allQueryValuesSuitable(audience.conditions, query);
  }, false);
