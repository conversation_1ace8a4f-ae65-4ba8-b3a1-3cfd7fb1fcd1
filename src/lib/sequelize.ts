import { FindOptions, WhereOptions, Op } from 'sequelize';
import models from '../models';

type parsedT = FindOptions<WhereOptions>;

const DEFAULTS = {
  pageSize: 50,
  pageNo: 1,
  offset: 0,
};

const buildQuery: any = raw => {
  const pageSize = raw.pageSize || DEFAULTS.pageSize;
  const pageNo = raw.pageNo || DEFAULTS.pageNo;

  const query: parsedT = {
    order: [['createdAt', 'DESC']],
    limit: pageSize,
    offset: pageSize * (pageNo - 1) || DEFAULTS.offset,
  };

  if (raw.search) {
    const exact = raw.exact ? raw.exact.toLowerCase() === 'true' : false;
    const search = exact
      ? {
          name: raw.search,
        }
      : {
          name: {
            [Op.iLike]: `%${raw.search}%`,
          },
        };
    query.where = {
      ...query.where,
      ...search,
    };
  }

  if (raw.id__not) {
    query.where = {
      ...query.where,
      id: {
        [Op.not]: raw.id__not,
      },
    };
  }
  return query;
};

export default buildQuery;
