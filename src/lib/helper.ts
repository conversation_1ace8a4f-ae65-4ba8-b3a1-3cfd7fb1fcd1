/* eslint-disable no-empty-pattern */

import { pipe, map, intersection, curry, __ } from 'lodash/fp';

const compareHeadAndTail = operator => ([lval, rval]) => operator(lval, rval);

const negator = (isTrue, value) => (isTrue ? !value : value);

const convertYYYYMMDDtoUnix = str => new Date(str).getTime();

const curriedNegate = curry(negator);

const compareDate = operator => (lval, rval, negate) =>
  pipe([
    map(convertYYYYMMDDtoUnix),
    compareHeadAndTail(operator),
    curriedNegate(__, negate),
  ])([lval, rval]);

function safeArray(input: string | string[]) {
  if (Array.isArray(input)) {
    return input;
  }

  return [].concat(input.replace(/\n/g, '').replace(/ /g, '').split(','));
}

const hasIntersection = (lval, rval) =>
  intersection(safeArray(lval), safeArray(rval)).length > 0;

const equals = (lval, rval, negate) => negator(lval === rval, negate);
const includes = (lval, rval, negate) =>
  negator(hasIntersection(lval, rval), negate);

export { compareDate, equals, includes };
