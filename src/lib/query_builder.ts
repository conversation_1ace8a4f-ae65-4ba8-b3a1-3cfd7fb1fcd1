import Sequelize from 'sequelize';
import { omitBy, isNil } from 'lodash';

const SORT = {
  1: 'ASC',
  '-1': 'DESC',
};

const SequelizeOpMap = {
  gte: Sequelize.Op.gte,
  gt: Sequelize.Op.gt,
  lt: Sequelize.Op.lt,
  lte: Sequelize.Op.lte,
  eq: Sequelize.Op.eq,
};

const setSearch = value => {
  if (!value || value === '') return null;
  return {
    where: {
      name: {
        [Sequelize.Op.like]: `${value}%`,
      },
    },
  };
};

const setOrder = value => {
  const order: Array<string[]> = [];
  Object.keys(value).forEach(key => {
    const sortOrder = SORT[value[key]];
    if (sortOrder) {
      order.push([key, sortOrder]);
    }
  });
  return {
    order,
  };
};

const setDateFilter = (value, field) => {
  const keys = Object.keys(value);

  const query = keys.reduce(
    (acc, key) => ({
      ...acc,
      [SequelizeOpMap[key]]: value[key],
    }),
    {}
  );

  return {
    where: {
      [field]: query,
    },
  };
};

const cleanObj = (x: { [key: string]: any }): { [key: string]: {} } =>
  omitBy(x, isNil);

const queryFields = {
  search: setSearch,
  sortBy: setOrder,
  createdAt: value => setDateFilter(value, 'createdAt'),
  updatedAt: value => setDateFilter(value, 'updatedAt'),
};

type QueryT = {
  supplierId?: string;
  search?: string;
  sortBy?: { [key: string]: string };
  createdAt?: string;
  updatedAt?: string;
  pageNo?: number;
  pageSize?: number;
  hostId?: string;
};

export const buildQuery = (rawQuery: QueryT) => {
  const { supplierId, pageNo, pageSize, hostId } = rawQuery;

  const query = Object.keys(rawQuery).reduce(
    (acc: any, key) => {
      const queryFn = queryFields[key];
      if (!queryFn) return acc;
      const value = rawQuery[key];
      const subQuery = queryFn(value);
      let updatedWhere = acc.where;
      let updatedOrder = acc.order;

      if (subQuery) {
        const { where, order } = subQuery;
        if (where) {
          updatedWhere = { ...acc.where, ...where };
        }
        if (order && order.length) {
          updatedOrder = [...acc.order, ...order];
        }
      }

      const filter = {
        where: updatedWhere,
        order: updatedOrder,
      };

      return filter;
    },
    {
      where: {
        supplierId,
        hostId,
      },
      order: [],
    }
  );

  const offsetPage = pageNo ? pageNo - 1 : 0;
  const limit = pageSize || 50;
  const offset = offsetPage * limit;

  const cleanedWhere = cleanObj(query.where);

  const finalQuery = {
    where: cleanedWhere,
    order: query.order,
    limit,
    offset,
  };

  return cleanObj(finalQuery);
};
