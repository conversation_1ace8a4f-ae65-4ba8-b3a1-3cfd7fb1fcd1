import { Model, Sequelize, DataTypes } from 'sequelize';
import { associable } from '../types';

export interface PromotionsUsageModel {
  id?: string;
  promotionId: string;
  supplierId: string;
  retailerId: string;
  userId: string;
  purchaserId: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export interface PromotionsUsageInstance
  extends Model<PromotionsUsageModel>,
    PromotionsUsageModel {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    promotionId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'promotion_id',
    },
    supplierId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'supplier_id',
    },
    purchaserId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'purchaser_id',
    },
    retailerId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'retailer_id',
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'user_id',
    },
    createdAt: { type: DataTypes.DATE, field: 'created_at' },
    updatedAt: { type: DataTypes.DATE, field: 'updated_at' },
    deletedAt: { type: DataTypes.DATE, field: 'deleted_at' },
  };

  const PromotionsUsage: associable<PromotionsUsageInstance> = sequelize.define<PromotionsUsageInstance>(
    'PromotionUsage',
    fields,
    {
      timestamps: true,
      tableName: 'promotions_usage',
      paranoid: true,
      underscored: true,
    }
  );

  PromotionsUsage.associate = models => {
    PromotionsUsage.belongsTo(models.Promotion, {
      as: 'promotions',
      foreignKey: 'promotion_id',
    });
  };

  return PromotionsUsage;
};
