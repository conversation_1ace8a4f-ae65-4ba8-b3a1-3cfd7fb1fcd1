import { Model, Sequelize, DataTypes } from 'sequelize';
import { associable } from '../types';

export interface ExperimentAudienceAttributes {
  id?: string;
  experimentId: string;
  audienceId: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export interface ExperimentAudienceInstance
  extends Model<ExperimentAudienceAttributes>,
    ExperimentAudienceAttributes {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    experimentId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'experiment_id',
    },
    audienceId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'audience_id',
    },
    createdAt: {
      type: DataTypes.DATE,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      field: 'updated_at',
    },
    deletedAt: {
      type: DataTypes.DATE,
      field: 'deleted_at',
    },
  };

  const opts = {
    tableName: 'experiment_audiences',
    timestamps: true,
    underscored: true,
    paranoid: false,
  };

  const ExperimentAudience: associable<ExperimentAudienceInstance> = sequelize.define<ExperimentAudienceInstance>(
    'ExperimentAudience',
    fields,
    opts
  );

  ExperimentAudience.associate = models => {
    ExperimentAudience.belongsTo(models.Experiment, {
      as: 'experiment',
      foreignKey: 'experiment_id',
    });
    ExperimentAudience.belongsTo(models.Audience, {
      as: 'audience',
      foreignKey: 'audience_id',
    });
  };

  return ExperimentAudience;
};
