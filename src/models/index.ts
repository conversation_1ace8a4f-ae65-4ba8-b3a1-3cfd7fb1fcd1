import { Sequelize } from 'sequelize';
import * as env from '../config/env';
import logger from '../config/logger';
import conditionFactory from './condition';
import flagFactory from './flag';
import audienceFactory from './audience';
import flagAudienceFactory from './flag_audience';
import slotFactory from './slot';
import experimentFactory from './experiment';
import experimentAudienceFactory from './experiment_audience';
import promotionFactory from './promotion';
import promotionsUsageFactory from './promotions_usage';

const { DATABASE_URI: url, LOG_LEVEL } = env;
const config = {
  logging: LOG_LEVEL === 'debug' ? l => logger.debug(l) : false,
};

export const sequelize = new Sequelize(url, config);

const Audience = audienceFactory(sequelize);
const Condition = conditionFactory(sequelize);
const Experiment = experimentFactory(sequelize);
const ExperimentAudience = experimentAudienceFactory(sequelize);
const Flag = flagFactory(sequelize);
const FlagAudience = flagAudienceFactory(sequelize);
const Slot = slotFactory(sequelize);
const Promotion = promotionFactory(sequelize);
const PromotionsUsage = promotionsUsageFactory(sequelize);

const db = {
  sequelize,
  Sequelize,

  Audience,
  Condition,
  Experiment,
  ExperimentAudience,
  Flag,
  FlagAudience,
  Slot,
  Promotion,
  PromotionsUsage,
};

Object.keys(db).forEach(modelName => {
  if ('associate' in db[modelName]) {
    const klass = db[modelName];
    klass.associate(db, klass);
  }
});

export default db;

export {
  Audience,
  Condition,
  Experiment,
  ExperimentAudience,
  Flag,
  FlagAudience,
  Slot,
  Promotion,
  PromotionsUsage,
};
