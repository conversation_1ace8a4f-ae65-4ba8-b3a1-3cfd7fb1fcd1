import { Model, Sequelize, DataTypes } from 'sequelize';
import { associable } from '../types';

export interface ConditionAttributes {
  id?: string;
  audienceId: string;
  key: string;
  operator: 'includes' | 'equals' | 'not_includes' | 'not_equals';
  rval: any;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export interface ConditionInstance
  extends Model<ConditionAttributes>,
    ConditionAttributes {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    audienceId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'audience_id',
    },
    key: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: '',
    },
    operator: {
      type: DataTypes.ENUM('equals', 'includes', 'not_includes', 'not_equals'),
      allowNull: false,
      defaultValue: 'equals',
    },
    rval: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
    },
    createdAt: { type: DataTypes.DATE, field: 'created_at' },
    updatedAt: { type: DataTypes.DATE, field: 'updated_at' },
    deletedAt: { type: DataTypes.DATE, field: 'deleted_at' },
  };

  const opts = {
    tableName: 'conditions',
    timestamps: true,
    underscored: true,
    paranoid: true,
  };

  const Condition: associable<ConditionInstance> = sequelize.define<ConditionInstance>(
    'Condition',
    fields,
    opts
  );

  Condition.associate = models => {
    Condition.belongsTo(models.Audience, {
      as: 'audience',
    });
  };

  return Condition;
};
