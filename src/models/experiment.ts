import { Model, Sequelize, DataTypes } from 'sequelize';
import C from '../constants';
import { AudienceInstance } from './audience';
import { associable } from '../types';

export interface ExperimentAttributes {
  id?: string;
  name: string;
  slotId?: string;
  meta?: any;
  priority?: number;
  active?: boolean;
  exclusive?: boolean;
  limit?: number;
  interval?: 'total' | 'day' | 'week' | 'month' | 'year';
  startAt?: string;
  endAt?: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  userId?: string;
  updatedById?: string;

  audiences?: AudienceInstance[];
}

export interface ExperimentInstance
  extends Model<ExperimentAttributes>,
    ExperimentAttributes {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: '',
    },
    slotId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'slot_id',
    },
    meta: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },

    priority: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    exclusive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },

    limit: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    interval: {
      type: DataTypes.ENUM(...C.EXPERIMENT_INTERVALS),
      allowNull: false,
      defaultValue: 'total',
    },

    startAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'start_at',
    },
    endAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
      field: 'end_at',
    },

    createdAt: {
      type: DataTypes.DATE,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      field: 'updated_at',
    },
    deletedAt: {
      type: DataTypes.DATE,
      field: 'deleted_at',
    },
    userId: {
      type: DataTypes.UUID,
      field: 'user_id',
      allowNull: true,
    },
    updatedById: {
      type: DataTypes.UUID,
      field: 'updated_by_id',
      allowNull: true,
    },
  };

  const opts = {
    tableName: 'experiments',
    timestamps: true,
    underscored: true,
    paranoid: true,
  };

  const Experiment: associable<ExperimentInstance> = sequelize.define<ExperimentInstance>(
    'Experiment',
    fields,
    opts
  );

  Experiment.associate = models => {
    Experiment.belongsToMany(models.Audience, {
      as: 'audiences',
      through: models.ExperimentAudience,
    });
    Experiment.belongsTo(models.Slot, {
      as: 'slot',
    });
  };

  return Experiment;
};
