import { Model, Sequelize, DataTypes } from 'sequelize';
import { associable } from '../types';

export interface SlotAttributes {
  id?: string;
  name: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export interface SlotInstance extends Model<SlotAttributes>, SlotAttributes {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
      defaultValue: DataTypes.UUIDV4,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },

    createdAt: {
      type: DataTypes.DATE,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      field: 'updated_at',
    },
    deletedAt: {
      type: DataTypes.DATE,
      field: 'deleted_at',
    },
  };

  const opts = {
    tableName: 'slots',
    underscored: true,
    paranoid: true,
  };

  const Slot: associable<SlotInstance> = sequelize.define<SlotInstance>(
    'Slot',
    fields,
    opts
  );

  Slot.associate = models => {
    Slot.hasMany(models.Experiment, {
      as: 'experiments',
    });
  };

  return Slot;
};
