import { Model, Sequelize, DataTypes } from 'sequelize';
import { associable } from '../types';

export interface PromotionAttributes {
  id?: string;
  name: string;
  description: string;
  conditions: object;
  effects: object;
  hostId?: string;
  coupon?: string;
  sponsored?: boolean;
  usage: number | null;
  createdById: string;
  updatedById: string;
  startAt: string;
  finishAt?: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export enum CombinatorEnum {
  and,
  any,
  equal,
  gt,
  gte,
  lt,
  lte,
  oneOf,
  or,
}

export type Combinator = keyof typeof CombinatorEnum;

export interface PromotionModel extends PromotionAttributes {
  createdAt: string;
  updatedAt: string;
  sponsored: boolean;
}

export interface PromotionInstance
  extends Model<PromotionModel>,
    PromotionModel {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    coupon: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      defaultValue: '',
    },
    conditions: {
      type: DataTypes.JSONB,
      allowNull: false,
    },
    effects: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: '[]',
    },
    hostId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'host_id',
    },
    sponsored: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    usage: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: null,
    },
    createdById: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'created_by_id',
    },
    updatedById: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'updated_by_id',
    },
    startAt: { type: DataTypes.DATE, field: 'start_at' },
    finishAt: { type: DataTypes.DATE, field: 'finish_at' },
    createdAt: { type: DataTypes.DATE, field: 'created_at' },
    updatedAt: { type: DataTypes.DATE, field: 'updated_at' },
    deletedAt: { type: DataTypes.DATE, field: 'deleted_at' },
  };
  const opts = {
    timestamps: true,
    tableName: 'promotions',
    paranoid: true,
    underscored: true,
  };

  const Promotion: associable<PromotionInstance> = sequelize.define<PromotionInstance>(
    'Promotion',
    fields,
    opts
  );

  return Promotion;
};
