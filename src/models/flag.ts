import { Model, Sequelize, DataTypes } from 'sequelize';
import { AudienceInstance } from './audience';
import { associable } from '../types';

export interface FlagAttributes {
  id?: string;
  name: string;
  alwaysEnabled: boolean;
  percentage: number;
  createdAt?: string;
  expiresAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  userId?: string;
  updatedById?: string;
  audiences?: AudienceInstance[];
}

export interface FlagInstance extends Model<FlagAttributes>, FlagAttributes {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'name',
    },
    percentage: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'percentage',
      defaultValue: 100,
    },
    alwaysEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
      field: 'always_enabled',
    },
    createdAt: { type: DataTypes.DATE, field: 'created_at' },
    expiresAt: { type: DataTypes.DATE, field: 'expires_at' },
    updatedAt: { type: DataTypes.DATE, field: 'updated_at' },
    deletedAt: { type: DataTypes.DATE, field: 'deleted_at' },
    userId: { type: DataTypes.UUID, field: 'user_id', allowNull: true },
    updatedById: {
      type: DataTypes.UUID,
      field: 'updated_by_id',
      allowNull: true,
    },
  };
  const opts = {
    tableName: 'flags',
    timestamps: true,
    underscored: true,
    paranoid: true,
  };
  const Flag: associable<FlagInstance> = sequelize.define<FlagInstance>(
    'Flag',
    fields,
    opts
  );

  Flag.associate = models => {
    Flag.belongsToMany(models.Audience, {
      as: 'audiences',
      through: models.FlagAudience,
    });
  };

  return Flag;
};
