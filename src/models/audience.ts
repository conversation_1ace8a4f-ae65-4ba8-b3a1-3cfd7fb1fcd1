import { Model, Sequelize, DataTypes } from 'sequelize';
import { ConditionInstance } from './condition';
import { associable } from '../types';

export interface AudienceAttributes {
  id?: string;
  name: string;
  conditions?: ConditionInstance[];
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  userId?: string;
  updatedById?: string;
}

export interface AudienceInstance
  extends Model<AudienceAttributes>,
    AudienceAttributes {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      field: 'updated_at',
    },
    deletedAt: {
      type: DataTypes.DATE,
      field: 'deleted_at',
    },
    userId: {
      type: DataTypes.UUID,
      field: 'user_id',
      allowNull: true,
    },
    updatedById: {
      type: DataTypes.UUID,
      field: 'updated_by_id',
      allowNull: true,
    },
  };

  const opts = {
    tableName: 'audiences',
    timestamps: true,
    underscored: true,
    paranoid: true,
  };

  const Audience: associable<AudienceInstance> = sequelize.define<AudienceInstance>(
    'Audience',
    fields,
    opts
  );

  Audience.associate = models => {
    Audience.hasMany(models.Condition, {
      as: 'conditions',
    });
    Audience.belongsToMany(models.Flag, {
      as: 'flags',
      through: models.FlagAudience,
    });
    Audience.belongsToMany(models.Experiment, {
      as: 'experiments',
      through: models.ExperimentAudience,
    });
  };

  return Audience;
};
