import { Sequelize, Model, DataTypes } from 'sequelize';
import { associable } from '../types';

export interface FlagAudienceAttributes {
  id?: string;
  flagId: string;
  audienceId: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export interface FlagAudienceInstance
  extends Model<FlagAudienceAttributes>,
    FlagAudienceAttributes {}

export default (sequelize: Sequelize) => {
  const fields = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    flagId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'flag_id',
    },
    audienceId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'audience_id',
    },
    createdAt: { type: DataTypes.DATE, field: 'created_at' },
    updatedAt: { type: DataTypes.DATE, field: 'updated_at' },
    deletedAt: { type: DataTypes.DATE, field: 'deleted_at' },
  };
  const opts = {
    tableName: 'flag_audiences',
    timestamps: true,
    underscored: true,
    paranoid: true,
  };
  const FlagAudience: associable<FlagAudienceInstance> = sequelize.define<FlagAudienceInstance>(
    'Flag',
    fields,
    opts
  );

  FlagAudience.associate = models => {
    FlagAudience.belongsTo(models.Flag, {
      as: 'flag',
      foreignKey: 'flag_id',
    });
    FlagAudience.belongsTo(models.Audience, {
      as: 'audience',
      foreignKey: 'audience_id',
    });
  };

  return FlagAudience;
};
