import httpContext from 'express-http-context';

import * as Serdes<PERSON>hangelog from '@ordermentum/serdes_changelog';

import { PromotionSchema } from '@ordermentum/serdes_promotion';
import { logger } from '../../config';
import { steveoKafka } from './index';

export enum Topics {
  PROMOTIONS = 'promotions',
}

const TOPIC_SERDES_MAP = <any>{
  [Topics.PROMOTIONS]: PromotionSchema,
};

/**
 * @description
 * Helper function to publish a Changelog event to Kafka
 * @param topic {Topic} Kafka topic
 * @param current {ModelAttributes} The attributes relating to the updated (or created) model
 * @param previous [ModelAttributes] The attributes relating to the previous model.
 */
const registerChange = <ModelAttributes = any, SerializedModel = any>(
  topic: Topics,
  current: ModelAttributes,
  previous?: ModelAttributes
) => {
  const serdesModel = TOPIC_SERDES_MAP[topic];
  if (!serdesModel) return;
  const userContext = httpContext.get('userContext');
  const serialized = SerdesChangelog.stringify<SerializedModel>(
    {
      current,
      previous,
      context: userContext,
    },
    serdesModel
  );
  steveoKafka.publish(topic, serialized).catch(e => {
    logger.error(e);
  });
};

export default {
  registerChange,
};
