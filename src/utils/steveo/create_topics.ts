import Logger from 'bunyan';
import { steveoKafka } from '.';

const log = Logger.createLogger({
  name: 'create-topics-script',
});

const run = async () => {
  log.info('Begin creating queues');
  await steveoKafka.runner().createQueues();
  log.info('Successfully created queues!');
  await steveoKafka.runner().disconnect();
};

run()
  .then(() => {
    process.exit();
  })
  .catch(err => {
    log.error(err);
    process.exit(1);
  });
