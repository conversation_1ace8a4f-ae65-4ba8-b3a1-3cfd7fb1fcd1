import Steveo from 'steveo';
import { Configuration } from 'steveo/lib/common';
import { logger } from '../../config';
import topics from './topics.json';

const inDocker = process.env.IN_DOCKER_CONTAINER === 'true';
const kafkaBoostrap = inDocker ? 'kafka:9092' : 'localhost:9092';

const kafkaConfig: Configuration = {
  engine: 'kafka',
  queuePrefix: process.env.NODE_ENV,
  upperCaseNames: true,
  securityProtocol:
    process.env.NODE_ENV === 'development' ? 'plaintext' : 'ssl',
  defaultPartitions: parseInt(process.env.KAFKA_DEFAULT_PARTITIONS ?? '3', 10),
  bootstrapServers: process.env.KAFKA_BOOTSTRAP_SERVERS || kafkaBoostrap,
  consumer: {
    global: {
      'group.id': process.env.KAFKA_CONSUMER_GROUP_ID || '',
    },
    topic: {},
  },
  waitToCommit: true,
};

export const steveoKafka = Steveo(kafkaConfig, logger, {});

steveoKafka.events.on('producer_success', async (topic: string, data: any) => {
  logger.info(data, { tags: { topic, event: 'producer_success' } });
});

steveoKafka.events.on('producer_failure', async (topic: string, ex: Error) => {
  logger.error(ex, { tags: { topic, event: 'producer_failure' } });
});

steveoKafka.events.on('task_failure', async (topic: string, ex: Error) => {
  logger.error(ex, { tags: { topic, event: 'task_failure' } });
});

topics.forEach(topic => steveoKafka.registerTopic(topic));
