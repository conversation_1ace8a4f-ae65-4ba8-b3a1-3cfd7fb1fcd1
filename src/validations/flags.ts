import maccabee from 'maccabee';

const {
  validatorFactory,
  matchers: { UUID4 },
  validators: {
    values: { isDate, isString, isBoolean },
    general: { nullable },
    string: { matches },
  },
} = maccabee;

export default validatorFactory({
  check: {
    id: [isString, matches(UUID4)].map(nullable),
    name: [isString],
    alwaysEnabled: [isBoolean].map(nullable),
    createdAt: [isDate].map(nullable),
    updatedAt: [isDate].map(nullable),
  },
});
