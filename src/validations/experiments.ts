import maccabee from 'maccabee';
import C from '../constants';

const {
  validatorFactory,
  matchers: { UUID4 },
  validators: {
    values: {
      isPresent,
      isBoolean,
      isNumber,
      isDate,
      isString,
      isIn,
      isObject,
      isArray,
    },
    general: { nullable },
    string: { matches },
  },
} = maccabee;

export const each = (f, { errorMessage = null } = {}) => (
  k /* : string */,
  params /* : Promise */
) =>
  isArray(k, params)
    .then(() => Promise.all(params[k].map(f)))
    .then(() => Promise.resolve())
    .catch(() =>
      // eslint-disable-next-line prefer-promise-reject-errors
      Promise.reject({
        expected: errorMessage || 'for each element to be valid',
        received: params[k],
        key: k,
      })
    );

const hasLength = (key, params) => {
  if (!params[key].length) {
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject({
      expected: 'to have length > 0',
      received: params[key].length,
      key,
    });
  }
  return Promise.resolve();
};

const isUuid4 = val => {
  const isUuid = UUID4.test(val);
  if (!isUuid) {
    throw new Error();
  }
};

export default validatorFactory({
  check: {
    id: [isString, matches(UUID4)].map(nullable),
    name: [isString, hasLength],
    slotId: [isString, matches(UUID4)].map(nullable),
    meta: [isObject],
    priority: [
      isIn([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], {
        errorMessage: 'Integer between 0 and 10',
      }),
    ].map(nullable),
    active: [isBoolean].map(nullable),
    exclusive: [isBoolean].map(nullable),

    limit: [isNumber].map(nullable),
    interval: [isIn(C.EXPERIMENT_INTERVALS)].map(nullable),
    startAt: [isDate].map(nullable),

    // TODO: Validate that this is after startAt
    endAt: [isDate].map(nullable),

    audienceIds: [
      isPresent,
      each(isUuid4, { errorMessage: 'for each element to be a uuid' }),
    ],

    createdAt: [isDate].map(nullable),
    updatedAt: [isDate].map(nullable),
  },
});
