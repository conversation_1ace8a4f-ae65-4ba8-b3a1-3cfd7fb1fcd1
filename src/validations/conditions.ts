import maccabee from 'maccabee';

const {
  validatorFactory,
  matchers: { UUID4 },
  validators: {
    values: { isDate, isIn, isString, isBoolean },
    general: { nullable },
    string: { matches },
  },
} = maccabee;

const OPERATORS = [
  'includes',
  'equals',
  'not_includes',
  'not_equals',
  'lt',
  'lte',
  'gt',
  'gte',
];

export const createValidator = validatorFactory({
  check: {
    id: [isString, matches(UUID4)].map(nullable),
    audienceId: [matches(UUID4)],
    key: [isString],
    operator: [isString, isIn(OPERATORS)],
    createdAt: [isDate].map(nullable),
    updatedAt: [isDate].map(nullable),
  },
});

export const updateValidator = validatorFactory({
  check: {
    id: [isString, matches(UUID4)].map(nullable),
    audienceId: [matches(UUID4)].map(nullable),
    key: [isString].map(nullable),
    operator: [isString, isIn(OPERATORS)].map(nullable),
    createdAt: [isDate].map(nullable),
    updatedAt: [isDate].map(nullable),
  },
});

export const experimentConditionValidator = validatorFactory({
  check: {
    id: [isString, matches(UUID4)].map(nullable),
    audienceId: [matches(UUID4)].map(nullable),
    key: [isString],
    operator: [isString, isIn(OPERATORS)],
    createdAt: [isDate].map(nullable),
    updatedAt: [isDate].map(nullable),
  },
});
