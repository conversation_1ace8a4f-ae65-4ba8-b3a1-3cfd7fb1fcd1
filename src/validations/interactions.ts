import maccabee from 'maccabee';

const {
  validatorFactory,
  matchers: { UUID4 },
  validators: {
    values: { isIn, isString },
    string: { matches },
  },
} = maccabee;

const TYPES = ['dismissal', 'acceptance', 'impression'];

const validator = validatorFactory({
  check: {
    type: [isString, isIn(TYPES)],
    experimentId: [isString, matches(UUID4)],
    userId: [isString, matches(UUID4)],
  },
});

export default validator;
