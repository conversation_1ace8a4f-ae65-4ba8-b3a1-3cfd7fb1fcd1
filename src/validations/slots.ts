import maccabee from 'maccabee';

const {
  validatorFactory,
  matchers: { UUID4 },
  validators: {
    values: { isDate, isString },
    general: { nullable },
    string: { matches },
  },
} = maccabee;

export default validatorFactory({
  check: {
    id: [isString, matches(UUID4)].map(nullable),
    name: [isString],
    createdAt: [isDate].map(nullable),
    updatedAt: [isDate].map(nullable),
  },
});
