import maccabee from 'maccabee';
import { experimentConditionValidator } from './conditions';
import { each } from './experiments';

const {
  validatorFactory,
  matchers: { UUID4 },
  validators: {
    values: { isDate, isIn, isString, isBoolean },
    general: { nullable },
    string: { matches },
  },
} = maccabee;

const validator = validatorFactory({
  check: {
    id: [isString, matches(UUID4)].map(nullable),
    name: [isString],
    flagId: [isString, matches(UUID4)].map(nullable),
    conditions: [each(experimentConditionValidator)].map(nullable),
  },
});

export default validator;
