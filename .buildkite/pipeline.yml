steps:
  - name: Test
    command: .buildkite/test.sh
    if: build.message !~ /skip tests/
    plugins:
      - docker-compose#v3.13.0:
          run: test
          config: docker-compose-build.yml

  - name: ':eslint:'
    command: .buildkite/eslint.sh
    if: build.message !~ /skip tests/
    plugins:
      - docker-compose#v3.13.0:
          run: test
          config: docker-compose-build.yml

  - block: Deploy to Sandbox
    branches: '!develop !main'

  - name: ':package:'
    command: .buildkite/package.sh

  - wait

  - name: ':aws: Deploy AWS resources'
    command: |
      VALUES_YAML_FILE_PATH=helm/values-api-$${HELM_VALUES}.yaml \
        deploy-application-resources
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - wait

  - name: ":kubernetes: Rotate ASAP keys"
    command: rotate-keys --service "$(basename -s .git $(git remote get-url origin))"
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - wait

  - name: ':kubernetes: Database Migrations'
    command: .buildkite/migrate-helm.sh db
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - wait

  - name: ':kubernetes: Deploy'
    command: .buildkite/deploy-helm.sh
    env:
      WITH_DEPLOY_CREDENTIALS: true

