#!/bin/bash
set -euo pipefail

OM_SERVICE_ECR_REPOSITORY=581666996624.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com/ordermentum/${OM_SERVICE_NAME}

aws ecr get-login-password \
  --region "${AWS_DEFAULT_REGION}" \
  | docker login \
    --username AWS \
    --password-stdin "$OM_SERVICE_ECR_REPOSITORY"

cat Dockerfile \
  | envsubst \
  | docker build \
    -t "${OM_SERVICE_ECR_REPOSITORY}:${OM_DOCKER_TAG}" \
    -f - \
    .

docker push "${OM_SERVICE_ECR_REPOSITORY}:${OM_DOCKER_TAG}"
