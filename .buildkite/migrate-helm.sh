#!/bin/bash
set -e

MIGRATION_TYPE=$1

REPO_NAME="$(basename -s .git $(git remote get-url origin))"

case $MIGRATION_TYPE in
  db)
    export RELEASE_NAME="${REPO_NAME}-migrate-db"
    export MIGRATE_COMMAND={yarn\,run\,db:migrate}
    ;;
  indexer)
    # only some services use this migration type
    export RELEASE_NAME="${REPO_NAME}-migrate-index"
    export MIGRATE_COMMAND={yarn\,run\,index:migrate}
    ;;
  topics )
    export RELEASE_NAME="${REPO_NAME}-migrate-topics"
    export MIGRATE_COMMAND={yarn\,run\,topics:setup}
    ;;
  *)
    echo "UNKNOWN MIGRATION TYPE $MIGRATION_TYPE"
    exit 1
    ;;
esac

helm delete --namespace ${KUBE_NAMESPACE} ${RELEASE_NAME} || true

helm install ${RELEASE_NAME} ordermentum/node-migrate \
  --namespace ${KUBE_NAMESPACE} \
  --values ./helm/values-api-${HELM_VALUES}.yaml \
  --set image.tag=${OM_DOCKER_TAG} \
  --set migrateCommand=${MIGRATE_COMMAND} \
  --set nameOverride=${RELEASE_NAME} \
  --set-string env.datadogServiceVersion.value="${BUILDKITE_COMMIT:0:8}" \
  --wait

# Wait for job to complete
kubectl wait job \
  --namespace "${KUBE_NAMESPACE}" \
  --selector release="${RELEASE_NAME}" \
  --for=condition=complete \
  --timeout=900s \
  || {
   kubectl logs \
    --namespace "${KUBE_NAMESPACE}" \
    --selector release="${RELEASE_NAME}" \
    --tail=20 \
    && exit 1
  }
