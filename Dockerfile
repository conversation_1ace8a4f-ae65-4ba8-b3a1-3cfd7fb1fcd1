FROM ordermentum/service:18
MAINTAINER Ordermentum <<EMAIL>>

ARG NODE_ENV=development
ARG NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
ENV NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN

COPY ./package.json /usr/src/app
COPY ./yarn.lock /usr/src/app
COPY ./.npmrc /usr/src/app/.npmrc

RUN echo "\n//registry.npmjs.org/:_authToken=$NPM_AUTH_TOKEN" >> /usr/src/app/.npmrc && yarn

ADD . /usr/src/app

RUN yarn build

RUN rm ./.npmrc

EXPOSE 6446:6446
CMD ["node", "--max-http-header-size=65536", "./bin/app"]
