import { expect } from 'chai';
import { agent } from '../../helper';

describe('GET /', () => {
  it('should respond with 200', async () => {
    const response = await agent.post('/v1/events').send({});
    expect(response.status).to.equal(200);
  });
  it('should respond with 200 if userId provided', async () => {
    const response = await agent
      .post('/v1/events')
      .send({ userId: '123', traits: {} });
    expect(response.status).to.equal(200);
  });
  it('should respond with 200 even if traits missing', async () => {
    const response = await agent
      .post('/v1/events')
      .send({ userId: '123', traits: null });
    expect(response.status).to.equal(200);
  });
});
