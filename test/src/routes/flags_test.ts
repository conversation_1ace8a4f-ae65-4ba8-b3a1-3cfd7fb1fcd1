import { expect } from 'chai';
import sinon from 'sinon';
import uuid from 'uuid';
import faker from 'faker';
import qs from 'querystring';

import { agent, secret } from '../../helper';
import validFlag from '../../fixtures/flag';
import validAudience from '../../fixtures/audience';

import { Flag, Condition, Audience, FlagAudience } from '../../../src/models';

describe('GET /', () => {
  let sandbox;
  beforeEach(async () => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });
  it('should respond with 200', async () => {
    await validFlag({});
    const response = await agent
      .get('/v1/flags')
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.meta).not.be.undefined;
    expect(response.body.links).not.be.undefined;
    expect(response.body.data.length).to.not.equal(0);
  });
  it('should search using exact name if exact param is true', async () => {
    const name = faker.name.findName();
    await validFlag({
      name,
    });
    const queryParams = qs.stringify({
      search: name,
      exact: true,
    });
    const response = await agent
      .get(`/v1/flags?${queryParams}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.meta).not.be.undefined;
    expect(response.body.links).not.be.undefined;
    expect(response.body.data.length).to.equal(1);
    expect(response.body.data[0].name).to.equal(name);
  });

  it('should not give back results if not using exact name but exact param is true', async () => {
    const name = faker.name.findName();
    await validFlag({
      name,
    });
    const queryParams = qs.stringify({
      search: name.substring(0, name.length - 2),
      exact: true,
    });
    const response = await agent
      .get(`/v1/flags?${queryParams}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.meta).not.be.undefined;
    expect(response.body.links).not.be.undefined;
    expect(response.body.data.length).to.equal(0);
  });
  it('should give back results if not using exact name and exact param is false', async () => {
    const name = faker.name.findName();
    await validFlag({
      name,
    });
    const queryParams = qs.stringify({
      search: name.substring(0, name.length - 2),
      exact: false,
    });
    const response = await agent
      .get(`/v1/flags?${queryParams}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.meta).not.be.undefined;
    expect(response.body.links).not.be.undefined;
    expect(response.body.data.length).to.equal(1);
    expect(response.body.data[0].name).to.equal(name);
  });
});

describe('GET /check', () => {
  let sandbox;
  beforeEach(async () => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });
  /*
    NOTE: this route has a cache control header that interferes with local testing? < 1 min intervals
  */
  it('should respond with 200 & proper response status', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    const response = await agent.get('/v1/flags/check').query({
      flags: [flag.name],
      context: {
        supplierId: '1234',
      },
    });
    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      [flag.name]: true,
    });
  });

  it('should respond with 200 & proper response status', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'userId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['0000'],
      operator: 'not_includes',
    });

    const response = await agent.get('/v1/flags/check').query({
      flags: [flag.name],
      context: {
        userId: '1234',
        supplierId: ['1234', '4444'],
      },
    });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      [flag.name]: true,
    });
  });

  it('should respond with 200 & proper response status', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'userId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['0000'],
      operator: 'not_includes',
    });

    const response = await agent.post('/v1/flags/check').send({
      flags: [flag.name],
      context: {
        userId: '1234',
        supplierId: ['1234', '4444'],
      },
    });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      [flag.name]: true,
    });
  });

  it('should respond with 200 & proper response status (real example)', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'source',
      rval: ['ios'],
      operator: 'includes',
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'version',
      rval: ['1.2.3'],
      operator: 'includes',
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['1234'],
      operator: 'includes',
    });

    const response = await agent
      .get('/v1/flags/check')
      .set({
        'X-Requested-With': 'XMLHttpRequest',
        Expires: '-1',
        'Cache-Control': 'no-cache,no-store,must-revalidate,max-age=-1,private',
      })
      .query({
        flags: [
          'review',
          'user-invite',
          'dashboard_profile',
          'popular-products',
          flag.name,
          'add-ons',
        ],
        debug: true,
        context: {
          userId: '1234',
          source: 'ios',
          retailerId: '123',
          version: '1.2.3',
          supplierId: ['1234', '4444', '3333'],
        },
      });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      [flag.name]: true,
      'add-ons': false,
      dashboard_profile: false,
      'popular-products': false,
      review: false,
      'user-invite': false,
    });
  });

  it('should respond with 200 & proper response status', async () => {
    const [flag, created] = await Flag.findOrCreate({
      where: {
        name: 'retailer-merge',
      },
    });
    if (created) await flag.save();
    const [flag2, created2] = await Flag.findOrCreate({
      where: {
        name: 'flags',
      },
    });
    if (created2) await flag2.save();

    const audience = await Audience.create({
      name: faker.name.findName(),
    });

    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await FlagAudience.create({
      flagId: flag2.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'userId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    const response = await agent.get('/v1/flags/check').query({
      flags: ['flags', 'retailer-merge'],
      context: {
        userId: '1234',
      },
    });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      'retailer-merge': true,
      flags: true,
    });
  });

  it('should respond with 200 & proper response status when the lval or rval is a string for include', async () => {
    const [flag, created] = await Flag.findOrCreate({
      where: {
        name: 'retailer-merge',
      },
    });
    if (created) await flag.save();
    const [flag2, created2] = await Flag.findOrCreate({
      where: {
        name: 'flags',
      },
    });
    if (created2) await flag2.save();

    const audience = await Audience.create({
      name: faker.name.findName(),
    });

    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await FlagAudience.create({
      flagId: flag2.id,
      audienceId: audience.id,
    });

    const userId1 = uuid.v4();
    const userId2 = uuid.v4();

    await Condition.create({
      audienceId: audience.id,
      key: 'userId',
      rval: `${userId1},\n${userId2}`,
      operator: 'includes',
    });

    const response = await agent.get('/v1/flags/check').query({
      flags: ['flags', 'retailer-merge'],
      context: {
        userId: userId1,
      },
    });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      'retailer-merge': true,
      flags: true,
    });
  });

  it('should respond with 200 & proper response status', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });
    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    const response = await agent.get('/v1/flags/check').query({
      flags: [flag.name],
      context: {
        supplierId: 'somebodyelse',
      },
    });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      [flag.name]: false,
    });
  });

  it('should respond with proper response status for non existant flags', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    const response = await agent.get('/v1/flags/check').query({
      flags: ['WRONG_FLAG'],
      context: {
        supplierId: 'somebodyelse',
      },
    });

    expect(response.body).to.deep.equal({
      WRONG_FLAG: false,
    });
  });

  it('should respond with 200, log and respond with false on exception', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: { some: 'thing' },
      operator: 'includes',
    });

    const response = await agent.get('/v1/flags/check').query({
      flags: [flag.name],
      context: {
        supplierId: 'somebodyelse',
      },
    });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({ [flag.name]: false });
  });

  it('should respond with 200 cached', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    const flagStub = sandbox.spy(Flag, 'findAll');

    const response = await agent.get('/v1/flags/check').query({
      flags: [flag.name],
      context: {
        supplierId: 'somebodyelse',
      },
    });

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      [flag.name]: false,
    });

    const response2 = await agent.get('/v1/flags/check').query({
      flags: [flag.name],
      context: {
        supplierId: 'somebodyelse',
      },
    });

    expect(flagStub.callCount).to.equal(1);
    expect(response2.status).to.equal(200);
    expect(response2.body).to.deep.equal(response.body);
  });

  it('should respond with 200 & proper response status for multiple existant flags', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    const flag2 = await validFlag({});
    const audience2 = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag2.id,
      audienceId: audience2.id,
    });

    await Condition.create({
      audienceId: audience2.id,
      key: 'purchaserId',
      rval: ['3456'],
      operator: 'includes',
    });

    const response = await agent
      .get('/v1/flags/check')
      .query({
        flags: [flag.name, flag2.name],
        context: {
          supplierId: '34557',
          purchaserId: '3456',
        },
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.header['cache-control']).to.equal('public, max-age=60');
    expect(response.body).to.deep.equal({
      [flag.name]: false,
      [flag2.name]: true,
    });
  });

  it('should respond with 200 & proper response status for multiple existant & non existant flags', async () => {
    const flag = await validFlag({});
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });

    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: ['1234', '3455'],
      operator: 'includes',
    });

    const flag2 = await validFlag({});
    const audience2 = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag2.id,
      audienceId: audience2.id,
    });

    await Condition.create({
      audienceId: audience2.id,
      key: 'purchaserId',
      rval: ['3456'],
      operator: 'includes',
    });

    const response = await agent
      .get('/v1/flags/check')
      .query({
        flags: ['WRONG', flag2.name],
        context: {
          supplierId: '34557',
          purchaserId: '3456',
        },
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      WRONG: false,
      [flag2.name]: true,
    });
  });

  it('should respond with 200 & proper response for flags that are alwaysEnabled', async () => {
    const flag = await validFlag({ alwaysEnabled: true });

    const response = await agent
      .get('/v1/flags/check')
      .query({
        flags: ['WRONG', flag.name],
        context: {
          supplierId: '34557',
          purchaserId: '3456',
        },
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      WRONG: false,
      [flag.name]: true,
    });
  });

  it('should check the user-context when included and context is not included', async () => {
    const supplierId = uuid.v4();
    // eslint-disable-next-line no-undef
    const userContext = btoa(JSON.stringify({ supplierId }));

    const flag = await validFlag({ alwaysEnabled: false });
    const audience = await Audience.create({
      name: faker.name.findName(),
    });
    await FlagAudience.create({
      flagId: flag.id,
      audienceId: audience.id,
    });
    await Condition.create({
      audienceId: audience.id,
      key: 'supplierId',
      rval: [supplierId],
      operator: 'includes',
    });

    const response = await agent
      .get('/v1/flags/check')
      .query({
        flags: ['WRONG', flag.name],
      })
      .set('Authorization', `Bearer ${secret}`)
      .set('user-context', userContext);

    expect(response.body[flag.name]).to.equal(true);
  });
});

describe('GET /:id', () => {
  it('should respond with 200', async () => {
    const flag = await validFlag({});
    const response = await agent
      .get(`/v1/flags/${flag.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.body.id).to.equal(flag.id);
  });

  it('should respond with 404 if not found', async () => {
    const resp = await agent
      .get(`/v1/flags/${uuid.v4()}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(resp.body.message).to.equal('Not Found');
    expect(resp.status).to.equal(404);
  });
});

describe('POST /', () => {
  it('should respond with 200', async () => {
    const name = faker.random.alphaNumeric(10);
    const response = await agent
      .post(`/v1/flags/`)
      .send({
        name,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.name).to.equal(name);
    const created = await Flag.findByPk(response.body.id);
    expect(created.name).to.equal(name);
  });

  it('should respond with 400 if validation failed', async () => {
    const response = await agent
      .post(`/v1/flags/`)
      .send({
        name: undefined,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(400);
  });
});

describe('PUT /:id', () => {
  it('should respond with 200', async () => {
    const flag = await validFlag({});
    const name = faker.random.alphaNumeric(10);
    const response = await agent
      .put(`/v1/flags/${flag.id}`)
      .send({
        name,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.name).to.equal(name);
    const updated = await Flag.findByPk(flag.id);
    expect(updated.name).to.equal(name);
  });

  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .put(`/v1/flags/${id}`)
      .send({
        name: 'retailer',
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});

describe('POST /:id/add-user', () => {
  it('should respond with 200', async () => {
    const flag = await validFlag({});
    const response = await agent
      .post(`/v1/flags/${flag.id}/add-user`)
      .send({ userId: uuid.v4() })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({ status: 'ok' });
  });

  it('should update existing condition', async () => {
    const flag = await validFlag({});
    const audience = await validAudience({
      name: `${flag.name} - users enabled`,
    });

    const condition = await Condition.create({
      rval: [],
      key: 'userId',
      operator: 'includes',
      audienceId: audience.id,
    });
    const params = { userId: uuid.v4() };
    const response = await agent
      .post(`/v1/flags/${flag.id}/add-user`)
      .send(params)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({ status: 'ok' });
    await condition.reload();
    expect(Array.isArray(condition.rval)).to.equal(true);
    expect(condition.rval.length).to.equal(1);
  });
});

describe('DELETE /:id', () => {
  it('should respond with 200', async () => {
    const flag = await validFlag({});
    const response = await agent
      .delete(`/v1/flags/${flag.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(204);
    expect(response.body).to.deep.equal({});
    const deleted = await Flag.findByPk(flag.id);
    expect(deleted).to.be.null;
  });

  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .delete(`/v1/flags/${id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});
