import uuid from 'uuid';
import { expect } from 'chai';

import { agent, secret } from '../../helper';
import * as Promotion from '../../../src/actions/promotions';
import { validAuth } from '../../fixtures/auth';

describe('Promotions Usage', () => {
  let hostId: string;
  let coupon: string;

  beforeEach(() => {
    hostId = uuid.v4();
    coupon =
      Math.random().toString(36).substring(0, 5) +
      Math.random().toString(36).substring(0, 5);
    coupon = coupon.toUpperCase();
  });

  context('POST - /v1/promotions/usage', () => {
    it('should respond with 500 with empty payload', async () => {
      const response = await agent
        .post('/v1/promotions/usage/')
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(500);
    });

    it('should respond with 404 with invalid payload', async () => {
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: [],
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
        usage: 0,
      };

      await Promotion.create(
        validAuth({
          supplierPrivileges: {
            [hostId]: ['promotions-create'],
          },
        }),
        payload
      );

      const response = await agent
        .post('/v1/promotions/usage/')
        .send({
          supplierId: hostId,
          retailerId: uuid.v4(),
          userId: uuid.v4(),
          discounts: [
            {
              name: payload.name,
              coupon: 'try',
              conditions: payload.conditions,
              description: payload.description,
              hostId: payload.hostId,
            },
          ],
        })
        .set('Authorization', `Bearer ${secret}`);

      expect(response.status).to.equal(404);
    });

    it('should respond with 201 with valid payload', async () => {
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: [],
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
        usage: 0,
        sponsored: false,
      };

      await Promotion.create(
        validAuth({
          supplierPrivileges: {
            [hostId]: ['promotions-create'],
          },
        }),
        payload
      );

      const response = await agent
        .post('/v1/promotions/usage/')
        .send({
          supplierId: hostId,
          retailerId: uuid.v4(),
          userId: uuid.v4(),
          purchaserId: uuid.v4(),
          discounts: [
            {
              name: payload.name,
              coupon: payload.coupon,
              conditions: payload.conditions,
              description: payload.description,
              hostId: payload.hostId,
              sponsored: payload.sponsored,
            },
          ],
        })
        .set('Authorization', `Bearer ${secret}`);

      expect(response.status).to.equal(201);
    });
  });
});
