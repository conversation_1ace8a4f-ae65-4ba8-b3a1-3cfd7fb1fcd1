import { expect } from 'chai';
import uuid from 'uuid';
import faker from 'faker';
import qs from 'qs';

import { agent, secret, uniqueName } from '../../helper';
import validExperiment from '../../fixtures/experiment';
import validAudience from '../../fixtures/audience';

import models from '../../../src/models';

describe('Experiments /', () => {
  describe('GET /', () => {
    it('should respond with 200', async () => {
      await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });
      const response = await agent
        .get('/v1/experiments')
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.meta).not.to.equal(undefined);
      expect(response.body.links).not.to.equal(undefined);
      expect(response.body.data.length).to.be.greaterThan(1);
    });
    it('handles search', async () => {
      const name = `SEARCH-EXPERIMENTS-${uniqueName()}`;
      await validExperiment({
        name,
      });
      const qst = qs.stringify({ search: name });
      const response = await agent
        .get(`/v1/experiments?${qst}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.meta).not.to.equal(undefined);
      expect(response.body.links).not.to.equal(undefined);
      expect(response.body.data.length).to.equal(1);
    });
  });

  describe('GET /:id', () => {
    it('should respond with 404', async () => {
      const id = uuid.v4();
      const response = await agent
        .get(`/v1/experiments/${id}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(404);
    });
    it('should respond with 200', async () => {
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });
      const response = await agent
        .get(`/v1/experiments/${experiment.id}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
    });
  });

  describe('GET /discover', () => {
    it('should respond with empty if no experiments for payload', async () => {
      const response = await agent
        .get('/v1/experiments/discover')
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.data.length).to.equal(0);
    });

    it('returns experiments that satisfy target audiences', async () => {
      const user1 = uuid.v4();
      const name = uniqueName();
      const slot = await models.Slot.create({
        name,
      });
      const audience = await validAudience({
        name: uniqueName(),
      });
      await models.Condition.create({
        key: 'userId',
        rval: user1,
        operator: 'equals',
        audienceId: audience.id,
      });
      const meta = {
        title: 'Payment Conversion',
        link: 'http://example.com/{{ userId }}',
        description: 'Trying to get more users onto payment methods',
      };
      const experiment = await validExperiment({
        name: uniqueName(),
        slotId: slot.id,
        meta,
      });
      await models.ExperimentAudience.create({
        audienceId: audience.id,
        experimentId: experiment.id,
      });
      const qst = qs.stringify({
        slot: name,
        userId: user1,
      });
      const response = await agent
        .get(`/v1/experiments/discover?${qst}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.data.length).to.equal(1);
      expect(response.body.data[0].name).to.equal(experiment.name);
      expect(response.body.data[0].slotName).to.equal(slot.name);
      expect(response.body.data[0].slot).to.equal(slot.name);
      expect(response.body.data[0].meta.link).to.equal(
        `http://example.com/${user1}`
      );
    });

    it('returns experiments that satisfy target audiences, excludes experiments that do not', async () => {
      const user1 = uuid.v4();
      const user2 = uuid.v4();
      const name = uniqueName();
      const slot = await models.Slot.create({
        name,
      });
      const audience1 = await validAudience({
        name: uniqueName(),
      });
      await models.Condition.create({
        key: 'userId',
        rval: user1,
        operator: 'equals',
        audienceId: audience1.id,
      });
      const experiment1 = await validExperiment({
        name: uniqueName(),
        slotId: slot.id,
      });
      await models.ExperimentAudience.create({
        audienceId: audience1.id,
        experimentId: experiment1.id,
      });

      const audience2 = await validAudience({
        name: uniqueName(),
      });
      await models.Condition.create({
        key: 'userId',
        rval: user2,
        operator: 'equals',
        audienceId: audience2.id,
      });
      const experiment2 = await validExperiment({
        name: uniqueName(),
        slotId: slot.id,
      });
      await models.ExperimentAudience.create({
        audienceId: audience2.id,
        experimentId: experiment2.id,
      });

      const qst = qs.stringify({
        slot: name,
        userId: user1,
      });
      const response = await agent
        .get(`/v1/experiments/discover?${qst}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.data.length).to.equal(1);
    });

    it('excludes experiments that have been rejected', async () => {
      const user1 = uuid.v4();
      const user2 = uuid.v4();
      const name = uniqueName();
      const slot = await models.Slot.create({
        name,
      });
      const audience1 = await validAudience({
        name: uniqueName(),
      });
      await models.Condition.create({
        key: 'userId',
        rval: user1,
        operator: 'equals',
        audienceId: audience1.id,
      });
      const experiment1 = await validExperiment({
        name: uniqueName(),
        slotId: slot.id,
      });
      await models.ExperimentAudience.create({
        audienceId: audience1.id,
        experimentId: experiment1.id,
      });

      const audience2 = await validAudience({
        name: uniqueName(),
      });
      await models.Condition.create({
        key: 'userId',
        rval: user2,
        operator: 'equals',
        audienceId: audience2.id,
      });
      const experiment2 = await validExperiment({
        name: uniqueName(),
        slotId: slot.id,
      });
      await models.ExperimentAudience.create({
        audienceId: audience2.id,
        experimentId: experiment2.id,
      });

      const qst = qs.stringify({
        slot: name,
        userId: user1,
      });

      const response = await agent
        .get(`/v1/experiments/discover?${qst}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.data.length).to.equal(1);

      const dismissal = await agent
        .post(`/v1/interactions`)
        .send({
          userId: user1,
          experimentId: experiment1.id,
          type: 'dismissal',
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(dismissal.body).to.equal('1');
      expect(dismissal.status).to.equal(200);

      const afterdismissal = await agent
        .get(`/v1/experiments/discover?${qst}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(afterdismissal.status).to.equal(200);
      expect(afterdismissal.body.data.length).to.equal(0);
    });
  });

  describe('POST /', () => {
    it('should respond with 400 if validation failed', async () => {
      const response = await agent
        .post('/v1/experiments/')
        .send({
          name: undefined,
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(400);
    });

    it('should respond with 200, no audiences', async () => {
      const name = faker.random.alphaNumeric(10);
      const response = await agent
        .post('/v1/experiments')
        .send({
          name,
          meta: {},
          audienceIds: [],
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.name).to.equal(name);
      const created = await models.Experiment.findByPk(response.body.id);
      expect(created.name).to.equal(name);

      const joins = await models.ExperimentAudience.findAll({
        where: {
          experimentId: created.id,
        },
      });
      expect(joins.length).to.equal(0);
    });

    it('should respond with 200, with audiences', async () => {
      const name = faker.random.alphaNumeric(10);
      const audience = await validAudience({});
      const response = await agent
        .post('/v1/experiments')
        .send({
          name,
          meta: {},
          audienceIds: [audience.id],
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.name).to.equal(name);
      const created = await models.Experiment.findByPk(response.body.id);
      expect(created.name).to.equal(name);

      const joins = await models.ExperimentAudience.findAll({
        where: {
          experimentId: created.id,
        },
      });
      expect(joins.length).to.equal(1);
    });
  });

  describe('POST /dismiss', () => {
    it('should respond with 404 if validation failed', async () => {
      const response = await agent
        .post('/v1/experiments/dismiss')
        .send({
          userId: undefined,
          experimentId: undefined,
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(404);
    });

    it('should respond with 200', async () => {
      const response = await agent
        .post('/v1/experiments/dismiss')
        .send({
          userId: uuid.v4(),
          experimentId: uuid.v4(),
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
    });
  });

  describe('PUT /:id', () => {
    it('should respond with 400 if validation failed', async () => {
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });
      const response = await agent
        .put(`/v1/experiments/${experiment.id}`)
        .send({
          name: undefined,
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(400);
    });
    it('should respond with 404 if not found', async () => {
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });
      const response = await agent
        .put(`/v1/experiments/${uuid.v4}`)
        .send({
          name: 'MAGIC',
          meta: {},
          audienceIds: [],
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(400);
    });

    it('should respond with 200, no audiences', async () => {
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });
      const name = faker.random.alphaNumeric(10);
      const response = await agent
        .put(`/v1/experiments/${experiment.id}`)
        .send({
          name,
          meta: {},
          audienceIds: [],
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.name).to.equal(name);
      const created = await models.Experiment.findByPk(response.body.id);
      expect(created.name).to.equal(name);

      const joins = await models.ExperimentAudience.findAll({
        where: {
          experimentId: experiment.id,
        },
      });
      expect(joins.length).to.equal(0);
    });

    it('should respond with 200, with audiences', async () => {
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });

      const name = uniqueName();
      const audience = await validAudience({});
      const response = await agent
        .put(`/v1/experiments/${experiment.id}`)
        .send({
          name,
          meta: {},
          audienceIds: [audience.id],
        })
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(200);
      expect(response.body.name).to.equal(name);
      const created = await models.Experiment.findByPk(response.body.id);
      expect(created.name).to.equal(name);

      const joins = await models.ExperimentAudience.findAll({
        where: {
          experimentId: experiment.id,
        },
      });
      expect(joins.length).to.equal(1);
    });
  });

  describe('DELETE /:id', () => {
    it('should respond with 404', async () => {
      const id = uuid.v4();
      const response = await agent
        .delete(`/v1/experiments/${id}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(404);
    });
    it('should respond with 200', async () => {
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });
      const response = await agent
        .delete(`/v1/experiments/${experiment.id}`)
        .set('Authorization', `Bearer ${secret}`);
      expect(response.status).to.equal(204);
    });
  });
});
