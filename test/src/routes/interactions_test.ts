import uuid from 'uuid';
import { expect } from 'chai';

import { agent, secret } from '../../helper';

describe('POST /', () => {
  it('should respond with 400 with empty payload', async () => {
    const response = await agent
      .post('/v1/interactions')
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(400);
  });
  it('should respond with 400 with empty payload', async () => {
    const response = await agent
      .post('/v1/interactions')
      .send({
        type: 'yeah boi',
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(400);
  });
  it('should respond with 200 with valid payload', async () => {
    const userId = uuid.v4();
    const experimentId = uuid.v4();
    const response = await agent
      .post('/v1/interactions')
      .send({
        type: 'dismissal',
        userId,
        experimentId,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.equal('1');
  });
  it('should respond with 200 with valid payload, 2nd dismissal', async () => {
    const userId = uuid.v4();
    const experimentId = uuid.v4();
    await agent
      .post('/v1/interactions')
      .send({
        type: 'dismissal',
        userId,
        experimentId,
      })
      .set('Authorization', `Bearer ${secret}`);
    const response = await agent
      .post('/v1/interactions')
      .send({
        type: 'dismissal',
        userId,
        experimentId,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.equal('2');
  });
});
