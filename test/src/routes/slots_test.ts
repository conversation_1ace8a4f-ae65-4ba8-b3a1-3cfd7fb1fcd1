import uuid from 'uuid';
import { expect } from 'chai';
import faker from 'faker';

import { agent, secret, uniqueName } from '../../helper';
import models from '../../../src/models';
import validSlot from '../../fixtures/slot';

describe('GET /', () => {
  it('should respond with 200', async () => {
    await validSlot({});
    const response = await agent
      .get('/v1/slots')
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.meta).not.be.undefined;
    expect(response.body.links).not.be.undefined;
    expect(response.body.data.length).to.not.equal(0);
  });
});

describe('GET /:id', () => {
  it('should respond with 200', async () => {
    const slot = await validSlot({});
    const response = await agent
      .get(`/v1/slots/${slot.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.id).to.equal(slot.id);
  });

  it('should respond with 404 if not found', async () => {
    const resp = await agent
      .get(`/v1/slots/${uuid.v4()}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(resp.body.message).to.equal('Not Found');
    expect(resp.status).to.equal(404);
  });
});

describe('POST /', () => {
  it('should respond with 200', async () => {
    const name = uniqueName();
    const response = await agent
      .post(`/v1/slots/`)
      .send({
        name,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.name).to.equal(name);

    const created = await models.Slot.findByPk(response.body.id);
    expect(created.name).to.equal(name);
  });

  it('should respond with 400 if validation failed', async () => {
    const response = await agent
      .post(`/v1/slots/`)
      .send({
        name: undefined,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(400);
  });
});

describe('PUT /:id', () => {
  it('should respond with 200', async () => {
    const name = uniqueName();
    const slot = await validSlot({});
    const response = await agent
      .put(`/v1/slots/${slot.id}`)
      .send({
        name,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.name).to.equal(name);
    const updated = await models.Slot.findByPk(slot.id);
    expect(updated.name).to.equal(name);
    expect(response.status).to.equal(200);
  });
  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .put(`/v1/slots/${id}`)
      .send({
        name: 'retailer',
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});

describe('DELETE /:id', () => {
  it('should respond with 200', async () => {
    const slot = await validSlot({});
    const response = await agent
      .delete(`/v1/slots/${slot.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(204);
    expect(response.body).to.deep.equal({});
    const deleted = await models.Slot.findByPk(slot.id);
    expect(deleted).to.be.null;
  });

  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .delete(`/v1/slots/${id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});
