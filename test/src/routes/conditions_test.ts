import uuid from 'uuid';
import { expect } from 'chai';

import models from '../../../src/models';
import { agent, secret } from '../../helper';
import validAudience from '../../fixtures/audience';
import validCondition from '../../fixtures/condition';

describe('GET /', () => {
  it('should respond with 200', async () => {
    await validCondition({});
    const response = await agent
      .get('/v1/conditions')
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.length).to.not.equal(0);
  });
});

describe('GET /:id', () => {
  it('should respond with 200', async () => {
    const condition = await validCondition({});
    const response = await agent
      .get(`/v1/conditions/${condition.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
  });

  it('should respond with 404 if not found', async () => {
    const resp = await agent
      .get(`/v1/conditions/${uuid.v4()}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(resp.body.message).to.equal('Not Found');
    expect(resp.status).to.equal(404);
  });
});

describe('POST /', () => {
  it('should respond with 200', async () => {
    const audience = await validAudience({});
    const response = await agent
      .post(`/v1/conditions/`)
      .send({
        audienceId: audience.id,
        key: 'supplier',
        operator: 'includes',
        rval: ['hello-flag'],
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.key).to.equal('supplier');
    const created = await models.Condition.findByPk(response.body.id);
    expect(created.operator).to.equal('includes');
  });
  it('should respond with 400 if validation failed', async () => {
    const audience = await validAudience({});
    const response = await agent
      .post(`/v1/conditions/`)
      .send({
        audienceId: audience.id,
        operator: 'includes',
        rval: ['hello-flag'],
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(400);
  });
});

describe('PUT /:id', () => {
  it('should respond with 200', async () => {
    const condition = await validCondition({});
    const response = await agent
      .put(`/v1/conditions/${condition.id}`)
      .send({
        key: 'retailer',
        rval: ['hello-flag'],
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.key).to.equal('retailer');
    const created = await models.Condition.findByPk(condition.id);
  });

  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .put(`/v1/conditions/${id}`)
      .send({
        key: 'retailer',
        rval: ['hello-flag'],
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});

describe('POST /:id/add-user', () => {
  it('should respond with 200', async () => {
    const condition = await validCondition({});
    const response = await agent
      .post(`/v1/conditions/${condition.id}/add-user`)
      .send({ userId: uuid.v4() })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({ status: 'ok' });
  });

  it('should update existing condition', async () => {
    const condition = await validCondition({});
    const conditionLength = condition.rval.length;
    const params = { userId: uuid.v4() };
    const response = await agent
      .post(`/v1/conditions/${condition.id}/add-user`)
      .send(params)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({ status: 'ok' });
    await condition.reload();
    expect(Array.isArray(condition.rval)).to.equal(true);
    expect(condition.rval.length).to.equal(conditionLength + 1);
  });

  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .post(`/v1/conditions/${id}/add-user`)
      .send({ userId: uuid.v4() })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});

describe('DELETE /:id', () => {
  it('should respond with 200', async () => {
    const condition = await validCondition({});
    const response = await agent
      .delete(`/v1/conditions/${condition.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(204);
    expect(response.body).to.deep.equal({});
    const deleted = await models.Condition.findByPk(condition.id);
    expect(deleted).to.be.null;
  });

  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .delete(`/v1/conditions/${id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});
