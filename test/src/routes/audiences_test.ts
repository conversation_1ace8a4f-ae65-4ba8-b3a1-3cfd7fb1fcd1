import uuid from 'uuid';
import { expect } from 'chai';
import qs from 'qs';

import { agent, secret, uniqueName } from '../../helper';

import models from '../../../src/models';

import validAudience from '../../fixtures/audience';
import validFlag from '../../fixtures/flag';

describe('GET /', () => {
  it('should respond with 200', async () => {
    await validAudience({});
    const response = await agent
      .get('/v1/audiences')
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.meta).not.be.undefined;
    expect(response.body.links).not.be.undefined;
    expect(response.body.data.length).to.not.equal(0);
  });
  it('handles search', async () => {
    const name = `SEARCH-AUDIENCES-${uniqueName()}`;
    await validAudience({
      name,
    });
    const qst = qs.stringify({ search: name });
    const response = await agent
      .get(`/v1/audiences?${qst}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.meta).not.be.undefined;
    expect(response.body.links).not.be.undefined;
    expect(response.body.data.length).to.equal(1);
  });
});

describe('GET /:id', () => {
  it('should respond with 200', async () => {
    const audience = await validAudience({});
    const response = await agent
      .get(`/v1/audiences/${audience.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.id).to.equal(audience.id);
  });

  it('should respond with 404 if not found', async () => {
    const resp = await agent
      .get(`/v1/audiences/${uuid.v4()}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(resp.body.message).to.equal('Not Found');
    expect(resp.status).to.equal(404);
  });
});

describe('POST /', () => {
  it('should respond with 200', async () => {
    const flag = await validFlag({});
    const response = await agent
      .post(`/v1/audiences/`)
      .send({
        name: 'hello',
        flagId: flag.id,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.name).to.equal('hello');
    const created = await models.Audience.findByPk(response.body.id);
    expect(created.name).to.equal('hello');
  });

  it('should respond with 400 if validation failed', async () => {
    const response = await agent
      .post(`/v1/audiences/`)
      .send({
        name: undefined,
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(400);
  });
});

describe('PUT /:id', () => {
  it('should respond with 200', async () => {
    const audience = await validAudience({});
    const response = await agent
      .put(`/v1/audiences/${audience.id}`)
      .send({
        name: 'hi',
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(200);
    expect(response.body.name).to.equal('hi');
    const updated = await models.Audience.findByPk(audience.id);
    expect(updated.name).to.equal('hi');
    expect(response.status).to.equal(200);
  });
  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .put(`/v1/audiences/${id}`)
      .send({
        name: 'retailer',
      })
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});

describe('DELETE /:id', () => {
  it('should respond with 200', async () => {
    const audience = await validAudience({});
    const response = await agent
      .delete(`/v1/audiences/${audience.id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(204);
    expect(response.body).to.deep.equal({});
    const deleted = await models.Audience.findByPk(audience.id);
    expect(deleted).to.be.null;
  });

  it('should respond with 404 if not found', async () => {
    const id = uuid.v4();
    const response = await agent
      .delete(`/v1/audiences/${id}`)
      .set('Authorization', `Bearer ${secret}`);
    expect(response.status).to.equal(404);
  });
});
