import { expect } from 'chai';
import validator from '../../../src/validations/flags';

describe('Flags validation', () => {
  it('should validate and do not throw error on correct body', async () => {
    const params = {
      name: '<PERSON><PERSON><PERSON>',
    };
    const result = await validator(params, {});
    expect(result).not.to.be.null;
  });

  it('should validate and throw error on incorrect body', async () => {
    const params = {
      name: undefined,
    };
    let err = false;
    try {
      await validator(params, {});
    } catch (ex) {
      err = true;
      expect(ex.data).to.deep.equal({
        name: [{ expected: 'string', key: 'name', received: 'undefined' }],
      });
    }
    expect(err).to.exist;
  });
});
