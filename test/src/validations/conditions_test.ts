import uuid from 'uuid';
import { expect } from 'chai';
import {
  createValidator,
  updateValidator,
} from '../../../src/validations/conditions';

describe('Conditions validation', () => {
  it('should validate and do not throw error on correct body', async () => {
    const params = {
      audienceId: uuid.v4(),
      key: 'supplierId',
      operator: 'includes',
      rval: [uuid.v4()],
    };

    const result = await createValidator(params, {});
    expect(result).not.to.be.null;
  });

  it('should validate and throw error on incorrect body', async () => {
    const params = {
      audienceId: uuid.v4(),
      operator: 'includes',
      rval: [uuid.v4()],
    };
    let err = false;
    try {
      await createValidator(params, {});
    } catch (ex) {
      err = true;
      expect(ex.data).to.deep.equal({
        key: [
          {
            expected: 'string',
            key: 'key',
            received: 'undefined',
          },
        ],
      });
      expect(err).to.exist;
    }
  });

  it('should validate and do not throw error on correct body for update', async () => {
    const params = {
      audienceId: uuid.v4(),
      key: 'supplierId',
      operator: 'includes',
      rval: [uuid.v4()],
    };
    const result = await updateValidator(params, {});
    expect(result).not.to.be.null;
  });

  it('should validate and throw error on incorrect body for update', async () => {
    const params = {
      audienceId: '123',
    };
    let err = false;
    try {
      await updateValidator(params, {});
    } catch (ex) {
      err = true;
      expect(ex.data).to.deep.equal({
        audienceId: [
          {
            expected: 'matching regex',
            key: 'audienceId',
            received: 'did not match regex',
          },
        ],
      });
    }
    expect(err).to.exist;
  });
});
