import uuid from 'uuid';
import { expect } from 'chai';
import validator from '../../../src/validations/interactions';

describe('Validations: Interactions', () => {
  it('throws error on invalid payload', async () => {
    const experimentId = uuid.v4();
    const userId = uuid.v4();
    const params = {
      experimentId,
      userId,
      type: 'YEAH',
    };
    let err = false;
    try {
      await validator(params, {});
    } catch (ex) {
      err = true;
      expect(ex.data).to.deep.equal({
        type: [
          {
            expected: '"YEAH" in ["dismissal","acceptance","impression"]',
            key: 'type',
            received: 'YEAH',
          },
        ],
      });
    }
    expect(err).to.exist;
  });

  it('returns valid payload', async () => {
    const experimentId = uuid.v4();
    const userId = uuid.v4();
    const type = 'dismissal';
    const params = {
      experimentId,
      userId,
      type,
    };
    const result = await validator(params, {});
    expect(result).not.to.be.null;
  });
});
