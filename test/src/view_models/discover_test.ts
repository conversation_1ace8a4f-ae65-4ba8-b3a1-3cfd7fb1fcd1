import { expect } from 'chai';
import DiscoverViewModel from '../../../src/view_models/discover';

describe('Discover View Model', () => {
  it('should return fields', () => {
    const payload = {
      id: '123',
      name: 'Retailer Profile Completion',
      query: { retailerId: '123' },
      meta: {
        title: 'Retailer Profile Completion',
        description: 'Yeah Nah Yeah',
        link: 'http://example.com/{{ retailerId }}',
      },
      slot: {
        name: 'dashboard',
      },
    };
    const result = DiscoverViewModel.build(payload).toJSON();
    const expected = {
      id: '123',
      slot: 'dashboard',
      slotName: 'dashboard',
      name: 'Retailer Profile Completion',
      meta: {
        title: 'Retailer Profile Completion',
        description: 'Yeah Nah Yeah',
        link: 'http://example.com/123',
      },
    };
    expect(result).to.deep.equal(expected);
  });
});
