import { expect } from 'chai';
import { includes } from '../../../src/lib/helper';

describe('Helpers.Comparison', () => {
  it('returns true for an intersection of arrays', () => {
    expect(includes(['1', '2', '3'], ['1'], false)).to.equal(true);
  });

  it('splits strings into arrays', () => {
    expect(includes('1, 2, \n3', '1', false)).to.equal(true);
  });

  it('returns true for an intersection of an array and a value', () => {
    expect(includes(['1', '2', '3'], '1', false)).to.equal(true);
  });

  it('returns true for an intersection of a value and an array', () => {
    expect(includes('1', ['1', '2', '3'], false)).to.equal(true);
  });

  it('returns true for an intersection of a two values', () => {
    expect(includes('1', '1', false)).to.equal(true);
  });

  it('returns false if there is no intersection', () => {
    expect(includes('1', '2', false)).to.equal(false);
  });

  it('returns false if there is no intersection', () => {
    expect(includes(['1'], ['2'], false)).to.equal(false);
  });

  it('returns false if there is no intersection', () => {
    expect(includes(['1', '2'], ['3', '4'], false)).to.equal(false);
  });
});
