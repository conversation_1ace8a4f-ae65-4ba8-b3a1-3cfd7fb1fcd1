import { expect } from 'chai';
import {
  isInATargetAudience,
  allQueryValuesSuitable,
  queryValueSatisfiesCondition,
} from '../../../src/lib';

describe('Helpers', () => {
  describe('queryValueSatisfiesCondition', () => {
    describe('gt', () => {
      expect(
        queryValueSatisfiesCondition(
          {
            rval: '2018-11-03',
            operator: 'gt',
          },
          '2018-12-03'
        )
      ).to.equal(true);
    });

    describe('gte', () => {
      expect(
        queryValueSatisfiesCondition(
          {
            rval: '2018-11-03',
            operator: 'gte',
          },
          '2018-11-03'
        )
      ).to.equal(true);
    });

    describe('lt', () => {
      expect(
        queryValueSatisfiesCondition(
          {
            rval: '2018-11-03',
            operator: 'lt',
          },
          '2018-09-03'
        )
      ).to.equal(true);
    });

    describe('lte', () => {
      expect(
        queryValueSatisfiesCondition(
          {
            rval: '2018-11-03',
            operator: 'lte',
          },
          '2018-10-03'
        )
      ).to.equal(true);
    });
  });

  describe('allQueryValuesSuitable', () => {
    it('returns false if not all values suitable', () => {
      const conditions = [
        {
          key: 'supplierId',
          rval: ['1234'],
          operator: 'includes',
        },
        {
          key: 'purchaserId',
          rval: '3456',
          operator: 'equals',
        },
      ];
      const query = {
        supplierId: '1234',
      };
      const result = allQueryValuesSuitable(conditions, query);
      expect(result).to.equal(false);
    });
    it('returns true if all values are suitable', () => {
      const conditions = [
        {
          key: 'supplierId',
          rval: ['1234'],
          operator: 'includes',
        },
        {
          key: 'purchaserId',
          rval: '3456',
          operator: 'equals',
        },
      ];
      const query = {
        supplierId: '1234',
        purchaserId: '3456',
      };
      const result = allQueryValuesSuitable(conditions, query);
      expect(result).to.equal(true);
    });
  });
  describe('isInATargetAudience', () => {
    it('single audience, single condition', () => {
      const flag = {
        name: 'myflag',
        audiences: [
          {
            name: 'myAudience 1',
            conditions: [
              {
                key: 'supplierId',
                rval: ['1234'],
                operator: 'includes',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        purchaserId: '3456',
        supplierId: '1234',
      });
      expect(resp).to.equal(true);
    });

    it('single audience, single condition (intersection)', () => {
      const flag = {
        name: 'myflag',
        audiences: [
          {
            name: 'myAudience 1',
            conditions: [
              {
                key: 'supplierId',
                rval: ['1234'],
                operator: 'includes',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        purchaserId: '3456',
        supplierId: ['1234'],
      });
      expect(resp).to.equal(true);
    });

    it('single audience, 2 conditions', () => {
      const flag = {
        name: 'myflag',
        audiences: [
          {
            name: 'myAudience 1',
            conditions: [
              {
                key: 'supplierId',
                rval: ['1234'],
                operator: 'includes',
              },
              {
                key: 'purchaserId',
                rval: '3456',
                operator: 'equals',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        purchaserId: '3456',
        supplierId: '1234',
      });
      expect(resp).to.equal(true);
    });

    it('single audience, 3 conditions, including negate', () => {
      const flag = {
        name: 'myflag',
        audiences: [
          {
            name: 'myAudience 1',
            conditions: [
              {
                key: 'supplierId',
                rval: ['1234'],
                operator: 'includes',
              },
              {
                key: 'purchaserId',
                rval: '3456',
                operator: 'equals',
              },
              {
                key: 'retailerId',
                rval: '0909',
                operator: 'not_equals',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        purchaserId: '3456',
        retailerId: '0908', // negate condition
        supplierId: '1234',
      });
      expect(resp).to.equal(true);
    });

    it('2 audiences', () => {
      const flag = {
        name: 'myflag',
        audiences: [
          {
            name: 'myAudience 1',
            conditions: [
              {
                key: 'supplierId',
                rval: ['1234'],
                operator: 'includes',
              },
              {
                key: 'purchaserId',
                rval: '3456',
                operator: 'equals',
              },
              {
                key: 'retailerId',
                rval: '0909',
                operator: 'not_equals',
              },
            ],
          },
          {
            name: 'myAudience 2',
            conditions: [
              {
                key: 'userId',
                rval: ['user1'],
                operator: 'includes',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        userId: 'user1',
      });
      expect(resp).to.equal(true);
    });

    it('should return false if payload is not in a target audience', () => {
      const flag = {
        name: 'myflag',
        audiences: [
          {
            name: 'myAudience 1',
            conditions: [
              {
                key: 'supplierId',
                rval: ['1234'],
                operator: 'includes',
              },
              {
                key: 'purchaserId',
                rval: '3456',
                operator: 'equals',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        purchaserId: '345623',
      });
      expect(resp).to.equal(false);
    });

    it('should return false if operator is invalid', () => {
      const flag = {
        name: 'myflag',
        audiences: [
          {
            name: 'myAudience 1',
            conditions: [
              {
                key: 'supplierId',
                rval: ['1234'],
                operator: 'something',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        supplierId: '1234',
      });
      expect(resp).to.equal(false);
    });
    it('handles includes_not', () => {
      const flag = {
        name: 'MY FLAG',
        audiences: [
          {
            name: 'myAudience',
            conditions: [
              {
                key: 'retailerId',
                rval: ['123'],
                operator: 'not_includes',
              },
            ],
          },
        ],
      };
      const resp = isInATargetAudience(flag.audiences, {
        retailerId: 'WILLYS-CAFE',
      });
      expect(resp).to.equal(true);
    });
    it('handles not_includes REAL LIFE', () => {
      const audiences = [
        {
          id: 'b6ce8dc5-bf19-4bb9-bcf4-2f2df9a8c5e6',
          name: 'Xero Audience',
          createdAt: '2018-11-19T02:47:08.892Z',
          updatedAt: '2018-11-19T02:47:08.892Z',
          conditions: [
            {
              id: '8932a9a0-e58f-4212-b4d5-db5cf5764a39',
              createdAt: '2018-11-19T02:47:08.903Z',
              updatedAt: '2018-11-19T03:05:56.256Z',
              audienceId: 'b6ce8dc5-bf19-4bb9-bcf4-2f2df9a8c5e6',
              key: 'retailerId',
              operator: 'not_includes',
              rval: [
                '5ea96ca3-9a45-4005-892d-eaf99a07eb0d',
                '3c3e19c0-b948-420e-a3c9-18a05802a4a7',
                '3d53516b-a4cd-4416-ba9a-e7d104af7ca0',
                '0ebd19f6-ddd3-4d45-9a42-7e6f9ec0fd77',
                '63384461-7f6d-4cbc-b5fa-cc66ea8eb712',
                '57ba078c-7721-499c-811c-e5368bad28d3',
                '04803435-c832-4898-9e45-d7e63e4f45bd',
                '86089086-55a2-499d-abf1-454631bebcb5',
                'd97a990a-3282-4d59-babe-8c2fb7fea3e0',
                '96e2e16b-7410-4197-964a-c36774ab8ecf',
                '1f40f5d0-8259-426f-9872-91f209671789',
                'f9953d93-5c11-4ed2-9b43-329996298853',
                '34f3d8f8-27ff-4467-ba7c-de316eb4e6e6',
                '004fa9bf-41eb-4d82-843c-4c141249d42f',
                'aad95584-4887-44ac-b253-136ea0e9df56',
                'a38d5bba-bee9-4d2b-a662-694e42ed37ad',
                '83eb4569-96cb-400d-931c-b861cc4aa791',
                '6bbc2f57-bd53-446a-a589-e292745385a6',
                'c798ecbd-71f2-4423-b779-430e95d66eda',
                'd650b5c7-f6a7-47e3-a8ea-7a5416fa6f07',
                '74552676-548d-47a9-8dc7-17794d4aee06',
                '0f3038c2-c5ec-4399-b73f-3b5a40d556b1',
                '01f89bdb-7af3-41e7-803b-620641f057d8',
                '45d4e665-c72e-4b80-8f47-a8776eb33af3',
                '989c5eba-bd63-4904-b13c-90665fea1055',
                'a16e4c4c-efa4-4e1c-a130-93cec99c7caf',
                '164840ee-e1b9-49fc-b39e-226c7d99519d',
                'eab43f02-497e-44fa-bc03-3ad4a937d6c1',
                'd3fd7e39-3144-43fc-a699-a1f31eb63384',
                '38253aa9-60c3-464f-b7e0-179033296be6',
                '3b2bc4d3-1b3e-4822-9a31-67e00b44834e',
                'a6b3a862-48dd-4343-8313-b3224a812fd3',
                'f472af15-eae6-402a-aaa6-4cc987b07dfc',
                'e0bfe293-8e09-4018-a441-4ef2a4e36b8c',
                '816c230c-26e1-4a4b-9e80-1579829cbe9c',
                '7b5161e7-4330-448b-97ab-d8839edf2603',
                'a4b75171-357d-464c-a03e-0a28874970b9',
                'd1fb07a6-cd62-4891-9598-698c81b6b33a',
                '6b827e08-adf1-4a1c-91c1-c199b85ed8b0',
              ],
            },
          ],
        },
      ];
      const flag = {
        name: 'MY FLAG',
        audiences,
      };
      const resp = isInATargetAudience(flag.audiences, {
        retailerId: 'WILLYS-CAFE',
      });
      expect(resp).to.equal(true);
    });
  });
});
