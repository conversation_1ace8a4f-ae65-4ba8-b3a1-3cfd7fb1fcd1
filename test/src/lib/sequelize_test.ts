import uuid from 'uuid';
import { Op } from 'sequelize';
import { expect } from 'chai';
import buildQuery from '../../../src/lib/sequelize';

describe('Helpers: sequelize', () => {
  describe('buildQuery', () => {
    it('default', () => {
      const query = buildQuery({});
      const expected = {
        limit: 50,
        offset: 0,
        order: [['createdAt', 'DESC']],
      };
      expect(query).to.deep.equal(expected);
    });
    it('handles pageSize and pageNo', () => {
      const query = buildQuery({
        pageSize: 10,
        pageNo: 3,
      });
      const expected = {
        limit: 10,
        offset: 20,
        order: [['createdAt', 'DESC']],
      };
      expect(query).to.deep.equal(expected);
    });
    it('handles search', () => {
      const query = buildQuery({
        pageSize: 10,
        pageNo: 3,
        search: 'MAGIC',
      });
      const expected = {
        limit: 10,
        offset: 20,
        order: [['createdAt', 'DESC']],
        where: {
          name: {
            [Op.iLike]: '%MAGIC%',
          },
        },
      };
      expect(query).to.deep.equal(expected);
    });
    it('handles id__not', () => {
      const id1 = uuid.v4();
      const query = buildQuery({
        pageSize: 10,
        pageNo: 3,
        id__not: [id1],
      });
      const expected = {
        limit: 10,
        offset: 20,
        order: [['createdAt', 'DESC']],
        where: {
          id: {
            [Op.not]: [id1],
          },
        },
      };
      expect(query).to.deep.equal(expected);
    });
    it('handles search and id__not', () => {
      const id1 = uuid.v4();
      const query = buildQuery({
        pageSize: 10,
        pageNo: 3,
        id__not: [id1],
        search: 'MAGIC',
      });
      const expected = {
        limit: 10,
        offset: 20,
        order: [['createdAt', 'DESC']],
        where: {
          id: {
            [Op.not]: [id1],
          },
          name: {
            [Op.iLike]: '%MAGIC%',
          },
        },
      };
      expect(query).to.deep.equal(expected);
    });
  });
});
