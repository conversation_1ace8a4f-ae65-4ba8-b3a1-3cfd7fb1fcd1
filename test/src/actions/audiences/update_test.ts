import { expect } from 'chai';
import update from '../../../../src/actions/audiences/update';
import models from '../../../../src/models';
import validAudience from '../../../fixtures/audience';
import validExperiment from '../../../fixtures/experiment';
import { uniqueName } from '../../../helper';

describe('Audience: update', () => {
  it('updates name', async () => {
    const originalName = uniqueName();
    const audience = await validAudience({
      name: originalName,
    });
    const newName = uniqueName();
    const payload = {
      name: newName,
    };
    await update(payload, audience.id);
    await audience.reload();

    expect(audience.name).to.equal(newName);
  });

  it('updates existing condition', async () => {
    const originalName = uniqueName();
    const audience = await validAudience({
      name: originalName,
    });
    const condition1 = await models.Condition.create({
      key: 'userId',
      rval: ['123'],
      operator: 'equals',
      audienceId: audience.id,
    });

    const newName = uniqueName();
    const payload = {
      name: newName,
      conditions: [
        {
          id: condition1.id,
          key: 'userId',
          operator: 'includes',
          rval: ['234'],
          audienceId: audience.id,
        },
      ],
    };

    await update(payload, audience.id);
    await audience.reload({
      include: [{ model: models.Condition, as: 'conditions' }],
    });

    expect(audience.name).to.equal(newName);
    expect(audience.conditions.length).to.equal(1);

    expect(audience.conditions[0].id).to.equal(condition1.id);

    await condition1.reload();
    expect(condition1.operator).to.equal('includes');
    expect(condition1.rval).to.deep.equal(['234']);
  });

  it('deletes conditions appropriately', async () => {
    const originalName = uniqueName();
    const audience = await validAudience({
      name: originalName,
    });
    const condition1 = await models.Condition.create({
      key: 'userId',
      rval: ['123'],
      operator: 'equals',
      audienceId: audience.id,
    });

    const newName = uniqueName();
    const payload = {
      name: newName,
      conditions: [
        {
          // not supplying id here, so this is a new condition
          key: 'userId',
          operator: 'includes',
          rval: ['234'],
          audienceId: audience.id,
        },
      ],
    };

    await update(payload, audience.id);
    await audience.reload({
      include: [{ model: models.Condition, as: 'conditions' }],
    });

    expect(audience.name).to.equal(newName);
    expect(audience.conditions.length).to.equal(1);

    expect(audience.conditions[0].id).not.to.equal(condition1.id);

    const deleted = await models.Condition.findByPk(condition1.id);
    expect(deleted).to.equal(null);

    const newCondition = audience.conditions[0];
    expect(newCondition.operator).to.equal('includes');
    expect(newCondition.rval).to.deep.equal(['234']);
  });
});
