import { expect } from 'chai';
import {
  buildData,
  updateAudienceConditions,
} from '../../../../src/actions/audiences/helper';
import validExperiment from '../../../fixtures/experiment';
import validAudience from '../../../fixtures/audience';
import models from '../../../../src/models';
import validCondition from '../../../fixtures/condition';

describe('Audience Helpers', () => {
  describe('buildData', () => {
    it('works', async () => {
      const existing = ['1', '2', '3', '4'];
      const incoming = ['3', '4', '5', '6'];
      const result = buildData(existing, incoming);
      const expected = {
        toDelete: ['1', '2'],
        toUpdate: ['3', '4'],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('updateAudienceConditions', () => {
    it('works', async () => {
      const audience = await validAudience({});
      const condition1 = await validCondition({
        audienceId: audience.id,
        key: 'supplierId',
      });
      const condition2 = await validCondition({
        audienceId: audience.id,
      });
      await audience.reload({
        include: [{ model: models.Condition, as: 'conditions' }],
      });

      const updated1 = { ...condition1.toJSON(), key: 'magic' };

      const condition3 = {
        key: 'myId',
        operator: 'equals',
        rval: '1337',
        audienceId: audience.id,
      };
      await updateAudienceConditions({
        existing: audience.conditions,
        incoming: [updated1, condition3],
        audienceId: audience.id,
      });

      await condition1.reload();
      expect(condition1.key).to.equal('magic');

      const deleted = await models.Condition.findByPk(condition2.id);
      expect(deleted).to.equal(null);

      const allConditions = await models.Condition.findAll({
        where: {
          audienceId: audience.id,
        },
      });
      expect(allConditions.length).to.equal(2);
    });
  });
});
