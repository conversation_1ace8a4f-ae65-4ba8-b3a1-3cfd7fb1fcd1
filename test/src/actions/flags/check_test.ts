import sinon from 'sinon';
import { expect } from 'chai';
import uuid from 'uuid';
import { getRollout } from '../../../../src/actions/flags/check';
import { Flag } from '../../../../src/models';
import { uniqueName } from '../../../helper';
import cache from '../../../../src/config/cache';

describe('check', () => {
  let sandbox;
  beforeEach(async () => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });
  it('is enabled', async () => {
    const name = uniqueName();
    const flag = await Flag.create({
      name,
      alwaysEnabled: false,
      percentage: 100,
    });

    sandbox.stub(cache, 'get').resolves({ [name]: 'IN' });

    const userId = uuid.v4();
    const result = await getRollout(flag, userId);
    expect(result).to.equal(true);
  });
});
