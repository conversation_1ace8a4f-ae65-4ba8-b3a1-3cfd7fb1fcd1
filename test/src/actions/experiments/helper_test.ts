import { expect } from 'chai';
import uuid from 'uuid';
import {
  updateExperimentAudiences,
  showExperiment,
  getCurrentExperiments,
} from '../../../../src/actions/experiments/helper';
import validExperiment from '../../../fixtures/experiment';
import validAudience from '../../../fixtures/audience';
import {
  incrementExperimentUser,
  incrementMultipleExperimentsUserImpressions,
} from '../../../../src/actions/interactions';

describe('Experiment Helpers', () => {
  describe('updateExperimentAudiences', () => {
    it('handles empty > empty', async () => {
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
      });
      const result = await updateExperimentAudiences({
        instance: experiment,
        newAudienceIds: [],
      });
      const expected = {
        deleted: 0,
        created: 0,
      };
      expect(result).to.deep.equal(expected);
    });
    it('handles 1 > empty', async () => {
      const audience = await validAudience({});
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
        audienceIds: [audience.id],
      });
      const result = await updateExperimentAudiences({
        instance: experiment,
        newAudienceIds: [],
      });
      const expected = {
        deleted: 1,
        created: 0,
      };
      expect(result).to.deep.equal(expected);
    });
    it('handles empty > 1', async () => {
      const audience = await validAudience({});
      const experiment = await validExperiment({
        meta: {
          title: 'Click here to complete your profile!',
        },
        audienceIds: [],
      });
      const result = await updateExperimentAudiences({
        instance: experiment,
        newAudienceIds: [audience.id],
      });
      const expected = {
        deleted: 0,
        created: 1,
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('showExperiment', () => {
    it('true, if limit is zero (ie. not limited)', () => {
      const impressions = 10;
      const limit = 0;
      const result = showExperiment(impressions, limit);
      expect(result).to.deep.equal(true);
    });
    it('true, if no impressions', () => {
      const impressions = 0;
      const limit = 10;
      const result = showExperiment(impressions, limit);
      expect(result).to.deep.equal(true);
    });
    it('true, if impressions < limit', () => {
      const impressions = 5;
      const limit = 10;
      const result = showExperiment(impressions, limit);
      expect(result).to.deep.equal(true);
    });
    it('false, if impressions > limit', () => {
      const impressions = 11;
      const limit = 10;
      const result = showExperiment(impressions, limit);
      expect(result).to.deep.equal(false);
    });
  });

  describe('getCurrentExperiments', () => {
    it('returns experiments', async () => {
      const experiment1 = await validExperiment({});
      const experiment2 = await validExperiment({});
      const experiment3 = await validExperiment({});
      const experiments = [experiment1, experiment2, experiment3];
      const userId = uuid.v4();
      const query = {
        userId,
      };
      const result = await getCurrentExperiments(experiments, query);
      expect(result.length).to.deep.equal(3);
    });
    it('excludes rejected experiments', async () => {
      const experiment1 = await validExperiment({});
      const experiment2 = await validExperiment({});
      const experiment3 = await validExperiment({});
      const experiments = [experiment1, experiment2, experiment3];
      const userId = uuid.v4();
      const query = {
        userId,
      };

      const rejected = await incrementExperimentUser('dismissals')(
        experiment1.id,
        userId
      );

      const result = await getCurrentExperiments(experiments, query);
      expect(result.length).to.deep.equal(2);

      const ids = result.map(r => r.id);
      expect(ids.includes(experiment1.id)).to.deep.equal(false);
    });
    it('excludes experiments where user impressions exceeds experiment limit', async () => {
      const experiment1 = await validExperiment({});
      const experiment2 = await validExperiment({
        limit: 1,
      });
      const experiment3 = await validExperiment({});
      const experiments = [experiment1, experiment2, experiment3];
      const userId = uuid.v4();
      const query = {
        userId,
      };

      const result1 = await getCurrentExperiments(experiments, query);
      expect(result1.length).to.deep.equal(3);

      await incrementMultipleExperimentsUserImpressions(experiments, query);

      const result2 = await getCurrentExperiments(experiments, query);
      expect(result2.length).to.deep.equal(2);

      const ids = result2.map(r => r.id);
      expect(ids.includes(experiment2.id)).to.deep.equal(false);
    });
  });
});
