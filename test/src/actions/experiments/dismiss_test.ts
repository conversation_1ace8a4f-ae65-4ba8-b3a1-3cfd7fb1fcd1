import sinon from 'sinon';
import { expect } from 'chai';
import uuid from 'uuid';
import faker from 'faker';
import dismiss from '../../../../src/actions/experiments/dismiss';
import models from '../../../../src/models';
import validExperiment from '../../../fixtures/experiment';
import { uniqueName } from '../../../helper';
import redis from '../../../../src/config/redis';

describe('discover', () => {
  let incrementSpy;
  let sandbox;
  beforeEach(async () => {
    sandbox = sinon.createSandbox();
    incrementSpy = sandbox.spy(redis, 'zincrby');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('increments when userId and experimentId is valid', async () => {
    const userId = uuid.v4();

    const name = uniqueName();
    const slot = await models.Slot.create({
      name,
    });

    const experiment = await validExperiment({
      name: uniqueName(),
      slotId: slot.id,
    });

    await dismiss({
      experimentId: experiment.id,
      userId,
    });

    expect(incrementSpy.called).to.equal(true);
  });
});
