import sinon from 'sinon';
import { expect } from 'chai';
import uuid from 'uuid';
import faker from 'faker';
import { discover } from '../../../../src/actions/experiments/discover';
import models from '../../../../src/models';
import validAudience from '../../../fixtures/audience';
import validExperiment from '../../../fixtures/experiment';
import { uniqueName } from '../../../helper';
import redis from '../../../../src/config/redis';
import cache from '../../../../src/config/cache';
import { getExperimentAllUserImpressions } from '../../../../src/actions/interactions';

const USER_AGENT =
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:65.0) Gecko/20100101 Firefox/65.0';

describe('discover', () => {
  let sandbox;
  beforeEach(async () => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });
  it('returns empty if slot not provided', async () => {
    const result = await discover(USER_AGENT, {});
    const expected = [];
    expect(result).to.deep.equal(expected);
  });
  it('returns empty if slot not found', async () => {
    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: faker.random.word(),
      }
    );
    const expected = [];
    expect(result).to.deep.equal(expected);
  });
  it('returns empty if no experiments found', async () => {
    const name = uniqueName();
    await models.Slot.create({
      name,
    });
    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: name,
      }
    );
    const expected = [];
    expect(result).to.deep.equal(expected);
  });

  it('returns empty if empty query', async () => {
    const user1 = uuid.v4();
    const name = uniqueName();
    await models.Slot.create({
      name,
    });
    const audience = await validAudience({
      name: uniqueName(),
    });
    await models.Condition.create({
      key: 'userId',
      rval: user1,
      operator: 'equals',
      audienceId: audience.id,
    });
    const experiment = await validExperiment({
      name: uniqueName(),
    });
    await models.ExperimentAudience.create({
      audienceId: audience.id,
      experimentId: experiment.id,
    });
    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: name,
      }
    );
    const expected = [];
    expect(result).to.deep.equal(expected);
  });

  it('returns empty if no experiments match query', async () => {
    const user1 = uuid.v4();
    const name = uniqueName();
    await models.Slot.create({
      name,
    });
    const audience = await validAudience({
      name: uniqueName(),
    });
    await models.Condition.create({
      key: 'userId',
      rval: user1,
      operator: 'equals',
      audienceId: audience.id,
    });
    const experiment = await validExperiment({
      name: uniqueName(),
    });
    await models.ExperimentAudience.create({
      audienceId: audience.id,
      experimentId: experiment.id,
    });
    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: name,
        userId: uuid.v4(),
      }
    );
    const expected = [];
    expect(result).to.deep.equal(expected);
  });

  it('returns experiments that satisfy target audiences', async () => {
    const user1 = uuid.v4();
    const name = uniqueName();
    const slot = await models.Slot.create({
      name,
    });
    const audience1 = await validAudience({
      name: uniqueName(),
    });
    await models.Condition.create({
      key: 'userId',
      rval: user1,
      operator: 'equals',
      audienceId: audience1.id,
    });
    const experiment1 = await validExperiment({
      name: uniqueName(),
      slotId: slot.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    const result = await discover(
      USER_AGENT,
      {
        deviceType: 'desktop',
      },
      {
        slot: name,
        userId: user1,
      }
    );
    expect(result.length).to.deep.equal(1);
  });

  it('returns experiments that satisfy profile', async () => {
    const user1 = uuid.v4();
    const name = uniqueName();
    const slot = await models.Slot.create({
      name,
    });
    const audience1 = await validAudience({
      name: uniqueName(),
    });
    await models.Condition.create({
      key: 'profile.isAwesome',
      rval: 'true',
      operator: 'equals',
      audienceId: audience1.id,
    });
    const experiment1 = await validExperiment({
      name: uniqueName(),
      slotId: slot.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    sandbox.stub(cache, 'get').resolves({ isAwesome: 'true' });

    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: name,
        userId: user1,
      }
    );
    expect(result.length).to.deep.equal(1);
  });

  it('returns experiments that satisfy profile campaign', async () => {
    const user1 = uuid.v4();
    const name = uniqueName();
    const slot = await models.Slot.create({
      name,
    });
    const audience1 = await validAudience({
      name: uniqueName(),
    });
    await models.Condition.create({
      key: 'profile.Campaign Group',
      rval: 'Baldico',
      operator: 'equals',
      audienceId: audience1.id,
    });
    const experiment1 = await validExperiment({
      name: uniqueName(),
      slotId: slot.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    sandbox.stub(cache, 'get').resolves({ 'Campaign Group': 'Baldico' });

    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: name,
        userId: user1,
      }
    );
    expect(result.length).to.deep.equal(1);
  });

  it('returns experiments that match user agent target audiences', async () => {
    const browser = 'Firefox';
    const name = uniqueName();
    const slot = await models.Slot.create({
      name,
    });
    const audience1 = await validAudience({
      name: uniqueName(),
    });
    await models.Condition.create({
      key: 'browser',
      rval: browser,
      operator: 'equals',
      audienceId: audience1.id,
    });
    const experiment1 = await validExperiment({
      name: uniqueName(),
      slotId: slot.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: name,
      }
    );
    expect(result.length).to.deep.equal(1);
  });

  it('returns experiments that satisfy target audiences, excludes experiments that do not', async () => {
    const user1 = uuid.v4();
    const user2 = uuid.v4();

    const name = uniqueName();
    const slot = await models.Slot.create({
      name,
    });
    const slot2 = await models.Slot.create({
      name: 'random-slot',
    });
    const audience1 = await validAudience({
      name: uniqueName(),
    });
    await models.Condition.create({
      key: 'userId',
      rval: user1,
      operator: 'equals',
      audienceId: audience1.id,
    });
    const experiment1 = await validExperiment({
      name: uniqueName(),
      slotId: slot.id,
    });

    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    const audience2 = await validAudience({
      name: uniqueName(),
      slotId: slot2.id,
    });
    await models.Condition.create({
      key: 'userId',
      rval: user2,
      operator: 'equals',
      audienceId: audience2.id,
    });
    const experiment2 = await validExperiment({
      name: uniqueName(),
      slotId: slot.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience2.id,
      experimentId: experiment2.id,
    });

    const result = await discover(
      USER_AGENT,
      {},
      {
        slot: name,
        userId: user1,
      }
    );
    expect(result.length).to.deep.equal(1);
  });

  it('real-life, query does not meet all audience conditions', async () => {
    const slot = await models.Slot.create({
      name: `dashboard-horizontal-${new Date().getTime()}`,
    });

    const supplier1 = '9b9cf5b6-0d06-436e-83c1-52d25e642e40';

    const user1 = 'a3c99869-1009-4182-8158-99830e27a972';
    const retailer1 = '020dd210-6928-4a4b-b97d-f819e58d280c';
    const retailer2 = '95f6640c-dbd6-4d47-9ffd-a3c9559f6042';
    const retailer3 = 'a2e2bbb4-bc75-471b-9ef2-57120faae44d';

    const experiment1 = await validExperiment({
      name: `real-life-${uniqueName()}`,
      slotId: slot.id,
    });

    /* --- audience1 ------------------------ */
    const audience1 = await validAudience({
      name: `real-life-audience-1-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'supplierId',
      rval: [supplier1],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.Condition.create({
      key: 'userId',
      rval: [user1],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer1, retailer2],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    /* --- audience2 ------------------------ */
    const audience2 = await validAudience({
      name: `real-life-audience-2-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer3],
      operator: 'includes',
      audienceId: audience2.id,
    });

    const withoutSupplierId = await discover(
      USER_AGENT,
      {},
      {
        slot: slot.name,
        userId: user1,
        retailerId: retailer1,
      }
    );
    expect(withoutSupplierId.length).to.deep.equal(0);
  });

  it('real-life, query meets all audience conditions', async () => {
    const slot = await models.Slot.create({
      name: `dashboard-horizontal-${new Date().getTime()}`,
    });

    const supplier1 = '9b9cf5b6-0d06-436e-83c1-52d25e642e40';

    const user1 = 'a3c99869-1009-4182-8158-99830e27a972';
    const retailer1 = '020dd210-6928-4a4b-b97d-f819e58d280c';
    const retailer2 = '95f6640c-dbd6-4d47-9ffd-a3c9559f6042';
    const retailer3 = 'a2e2bbb4-bc75-471b-9ef2-57120faae44d';

    const experiment1 = await validExperiment({
      name: `real-life-${uniqueName()}`,
      slotId: slot.id,
    });

    /* --- audience1 ------------------------ */
    const audience1 = await validAudience({
      name: `real-life-audience-1-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'supplierId',
      rval: [supplier1],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.Condition.create({
      key: 'userId',
      rval: [user1],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer1, retailer2],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    /* --- audience2 ------------------------ */
    const audience2 = await validAudience({
      name: `real-life-audience-2-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer3],
      operator: 'includes',
      audienceId: audience2.id,
    });

    const withSupplierId = await discover(
      USER_AGENT,
      {},
      {
        slot: slot.name,
        userId: user1,
        retailerId: retailer1,
        supplierId: supplier1,
      }
    );
    expect(withSupplierId.length).to.deep.equal(1);
  });

  it('stores impressions', async () => {
    const slot = await models.Slot.create({
      name: `dashboard-horizontal-${new Date().getTime()}`,
    });

    const supplier1 = '9b9cf5b6-0d06-436e-83c1-52d25e642e40';

    const user1 = uuid.v4();
    const user2 = uuid.v4();
    const retailer1 = uuid.v4();
    const retailer2 = uuid.v4();
    const retailer3 = uuid.v4();

    const experiment1 = await validExperiment({
      name: `impressions-${uniqueName()}`,
      slotId: slot.id,
    });

    /* --- audience1 ------------------------ */
    const audience1 = await validAudience({
      name: `real-life-audience-1-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'userId',
      rval: [user1, user2],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer1, retailer2],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    /* --- audience2 ------------------------ */
    const audience2 = await validAudience({
      name: `real-life-audience-2-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer3],
      operator: 'includes',
      audienceId: audience2.id,
    });

    const getSpy = sandbox.spy(redis, 'zscore');
    const incrementSpy = sandbox.spy(redis, 'zincrby');

    await discover(
      USER_AGENT,
      {},
      {
        slot: slot.name,
        userId: user1,
        retailerId: retailer1,
      }
    );
    await discover(
      USER_AGENT,
      {},
      {
        slot: slot.name,
        userId: user1,
        retailerId: retailer1,
      }
    );
    await discover(
      USER_AGENT,
      {},
      {
        slot: slot.name,
        userId: user2,
        retailerId: retailer1,
      }
    );

    const dismissalChecks = 3;
    const impressionChecks = 3;
    expect(getSpy.callCount).to.deep.equal(dismissalChecks + impressionChecks);
    expect(incrementSpy.callCount).to.deep.equal(3);

    const experimentImpressions = await getExperimentAllUserImpressions(
      experiment1.id
    );
    expect(experimentImpressions).to.deep.equal([user1, '2', user2, '1']);
  });

  it('returns empty after limit exceeded', async () => {
    const slot = await models.Slot.create({
      name: `dashboard-horizontal-${new Date().getTime()}`,
    });

    const user1 = uuid.v4();
    const user2 = uuid.v4();
    const retailer1 = uuid.v4();
    const retailer2 = uuid.v4();
    const retailer3 = uuid.v4();

    const experiment1 = await validExperiment({
      name: `limit-1-${uniqueName()}`,
      slotId: slot.id,
      limit: 1,
    });

    /* --- audience1 ------------------------ */
    const audience1 = await validAudience({
      name: `real-life-audience-1-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'userId',
      rval: [user1, user2],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer1, retailer2],
      operator: 'includes',
      audienceId: audience1.id,
    });
    await models.ExperimentAudience.create({
      audienceId: audience1.id,
      experimentId: experiment1.id,
    });

    /* --- audience2 ------------------------ */
    const audience2 = await validAudience({
      name: `real-life-audience-2-${uniqueName()}`,
    });
    await models.Condition.create({
      key: 'retailerId',
      rval: [retailer3],
      operator: 'includes',
      audienceId: audience2.id,
    });

    const getSpy = sandbox.spy(redis, 'zscore');
    const incrementSpy = sandbox.spy(redis, 'zincrby');

    const result1 = await discover(
      USER_AGENT,
      {},
      {
        slot: slot.name,
        userId: user1,
        retailerId: retailer1,
      }
    );
    expect(result1.length).to.deep.equal(1);

    const result2 = await discover(
      USER_AGENT,
      {},
      {
        slot: slot.name,
        userId: user1,
        retailerId: retailer1,
      }
    );
    expect(result2.length).to.deep.equal(0);

    const dismissalChecks = 2;
    const impressionChecks = 2;
    expect(getSpy.callCount).to.deep.equal(dismissalChecks + impressionChecks);
    expect(incrementSpy.callCount).to.deep.equal(1);

    const experimentImpressions = await getExperimentAllUserImpressions(
      experiment1.id
    );
    expect(experimentImpressions).to.deep.equal([user1, '1']);
  });
});
