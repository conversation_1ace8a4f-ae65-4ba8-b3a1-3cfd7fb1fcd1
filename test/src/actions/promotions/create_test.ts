import { expect } from 'chai';
import uuid from 'uuid';
import create from '../../../../src/actions/promotions/create';
import { validAuth } from '../../../fixtures/auth';

describe('Create Promotion', () => {
  let hostId;
  let auth;
  let coupon: string;
  beforeEach(() => {
    hostId = uuid.v4();
    auth = validAuth({
      supplierPrivileges: {
        [hostId]: ['promotions-create'],
      },
    });
    coupon =
      Math.random().toString(36).substring(0, 5) +
      Math.random().toString(36).substring(0, 5);
    coupon = coupon.toUpperCase();
  });

  context('With Access', () => {
    it('creates promotion with valid data', async () => {
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: {
          combinator: 'and',
          leftNode: { combinator: 'equal', leftNode: 'one', rightNode: 1 },
          rightNode: {
            combinator: 'any',
            leftNode: 'objList',
            rightNode: [2, 3],
            negate: true,
            modifier: '_access.c',
          },
        },
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(false);
      expect(promotion.coupon).to.equal(coupon);
    });

    it('creates promotion with sponsored set to true', async () => {
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: {
          combinator: 'and',
          leftNode: { combinator: 'equal', leftNode: 'one', rightNode: 1 },
          rightNode: {
            combinator: 'any',
            leftNode: 'objList',
            rightNode: [2, 3],
            negate: true,
            modifier: '_access.c',
          },
        },
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
        sponsored: true,
      };

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(true);
      expect(promotion.coupon).to.equal(coupon);
    });

    it('creates coupon promotion with one filter', async () => {
      const filter = [
        {
          field: {
            tag: 'lastOrderedSince',
            isProperty: false,
          },
          operator: 'equals',
          value: '5',
        },
      ];

      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: filter,
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(false);
      expect(promotion.coupon).to.equal(coupon);
      expect(promotion.conditions).to.deep.equal({
        leftNode: {
          leftNode: 'purchaser',
          tag: 'lastOrderedSince',
          combinator: 'equal',
          modifier: '_access.lastOrderedSince',
          rightNode: '5',
        },
        rightNode: {
          leftNode: 'supplierId',
          combinator: 'equal',
          rightNode: payload.hostId,
        },
        combinator: 'and',
      });
    });

    it('creates coupon promotion with no filter', async () => {
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: [],
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(false);
      expect(promotion.coupon).to.equal(coupon);
      expect(promotion.conditions).to.deep.equal([]);
    });

    it('Only allows unique coupon names per host id', async () => {
      const filter = [
        {
          field: {
            tag: 'lastOrderedSince',
            isProperty: false,
          },
          operator: 'equals',
          value: '5',
        },
      ];
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: filter,
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(false);
      expect(promotion.coupon).to.equal(coupon);
      expect(promotion.conditions).to.deep.equal({
        leftNode: {
          leftNode: 'purchaser',
          tag: 'lastOrderedSince',
          combinator: 'equal',
          modifier: '_access.lastOrderedSince',
          rightNode: '5',
        },
        rightNode: {
          leftNode: 'supplierId',
          combinator: 'equal',
          rightNode: payload.hostId,
        },
        combinator: 'and',
      });

      let e;
      try {
        await create(auth, payload);
      } catch (err) {
        e = err;
      }
      expect(e.statusCode).to.equal(400);
    });

    it('creates promotion with one filter', async () => {
      const filter = [
        {
          field: {
            tag: 'lastOrderedSince',
            isProperty: false,
          },
          operator: 'equals',
          value: '5',
        },
      ];

      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: filter,
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(false);
      expect(promotion.conditions).to.deep.equal({
        leftNode: {
          leftNode: 'purchaser',
          tag: 'lastOrderedSince',
          combinator: 'equal',
          modifier: '_access.lastOrderedSince',
          rightNode: '5',
        },
        rightNode: {
          leftNode: 'supplierId',
          combinator: 'equal',
          rightNode: payload.hostId,
        },
        combinator: 'and',
      });
    });

    it('creates promotion with multiple filters', async () => {
      const filter = [
        {
          field: {
            tag: 'lastOrderedSince',
            isProperty: false,
          },
          operator: 'equals',
          value: '5',
        },
        {
          field: {
            tag: 'firstOrder',
            isProperty: false,
          },
          operator: 'equals',
          value: 'true',
        },
      ];

      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: filter,
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      auth.context.suppliers = [payload.hostId];

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(false);
      expect(promotion.conditions).to.deep.equal({
        leftNode: {
          leftNode: {
            leftNode: 'purchaser',
            tag: 'lastOrderedSince',
            combinator: 'equal',
            modifier: '_access.lastOrderedSince',
            rightNode: '5',
          },
          rightNode: {
            leftNode: 'purchaser',
            tag: 'firstOrder',
            combinator: 'equal',
            modifier: '_access.firstOrder',
            rightNode: 'true',
          },
          combinator: 'and',
        },
        rightNode: {
          leftNode: 'supplierId',
          combinator: 'equal',
          rightNode: payload.hostId,
        },
        combinator: 'and',
      });
    });

    it('creates promotion with one filter that is a property', async () => {
      const filter = [
        {
          field: {
            tag: 'testgroup',
            isProperty: true,
          },
          operator: 'equals',
          value: '5',
        },
      ];

      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: filter,
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const promotion = await create(auth, payload);
      expect(promotion.createdById).to.equal(auth.id);
      expect(promotion.hostId).to.equal(payload.hostId);
      expect(promotion.sponsored).to.equal(false);
      expect(promotion.conditions).to.deep.equal({
        leftNode: {
          leftNode: 'purchaser',
          tag: 'testgroup',
          combinator: 'equal',
          modifier: '_access.properties.testgroup',
          rightNode: '5',
        },
        rightNode: {
          leftNode: 'supplierId',
          combinator: 'equal',
          rightNode: payload.hostId,
        },
        combinator: 'and',
      });
    });

    const usageValues = [0, null, 10];

    for (const usage of usageValues) {
      // eslint-disable-next-line no-loop-func
      it(`creates a coupon promotion with usage ${usage}`, async () => {
        const payload = {
          name: Math.random().toString(),
          description: '',
          conditions: [],
          effects: [{ target: 'cart', type: 'absolute', value: 10 }],
          createdById: uuid.v4(),
          updatedById: uuid.v4(),
          startAt: new Date(Date.now() - 1000).toISOString(),
          finishAt: new Date(Date.now() + 1000).toISOString(),
          hostId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          coupon,
          usage,
        };

        const promotion = await create(auth, payload);
        expect(promotion.createdById).to.equal(auth.id);
        expect(promotion.hostId).to.equal(payload.hostId);
        expect(promotion.sponsored).to.equal(false);
        expect(promotion.coupon).to.equal(coupon);
        expect(promotion.conditions).to.deep.equal([]);
        expect(promotion.usage).to.equal(payload.usage);
      });
    }

    it('should not accept usage as string', async () => {
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: [],
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
        usage: 'string',
      };

      try {
        await create(auth, payload);
      } catch ({ message: { details } }) {
        expect('"usage" must be a number').to.be.equal(details[0].message);
      }
    });
  });

  context('No access', () => {
    let user;
    beforeEach(() => {
      user = validAuth({
        supplierPrivileges: {
          [hostId]: ['some-access'],
        },
      });
    });

    it('Should throw a 403 error', async () => {
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: {
          combinator: 'and',
          leftNode: { combinator: 'equal', leftNode: 'one', rightNode: 1 },
          rightNode: {
            combinator: 'any',
            leftNode: 'objList',
            rightNode: [2, 3],
            negate: true,
            modifier: '_access.c',
          },
        },
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      let err;
      try {
        await create(user, payload);
      } catch (ex) {
        err = ex;
      }

      expect(err.message).to.equal('No privilege');
    });
  });

  context('Admin access', () => {
    it('Admin can create a promotion without a hostId', async () => {
      const adminAuth = validAuth({
        supplierPrivileges: {
          [hostId]: ['promotions-create'],
        },
        admin: true,
      });
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: {
          combinator: 'and',
          leftNode: { combinator: 'equal', leftNode: 'one', rightNode: 1 },
          rightNode: {
            combinator: 'any',
            leftNode: 'objList',
            rightNode: [2, 3],
            negate: true,
            modifier: '_access.c',
          },
        },
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId: undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      const promotion = await create(adminAuth, payload);
      expect(promotion.hostId).to.be.null;
    });

    it('Admin can create a promotion with a hostId', async () => {
      const adminAuth = validAuth({
        supplierPrivileges: {
          [hostId]: ['promotions-create'],
        },
        admin: true,
      });
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: {
          combinator: 'and',
          leftNode: { combinator: 'equal', leftNode: 'one', rightNode: 1 },
          rightNode: {
            combinator: 'any',
            leftNode: 'objList',
            rightNode: [2, 3],
            negate: true,
            modifier: '_access.c',
          },
        },
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      const promotion = await create(adminAuth, payload);
      expect(promotion.hostId).to.be.eq(hostId);
    });

    it('Non-admin cannot create a promotion without a hostId', async () => {
      const nonAdminAuth = validAuth({
        supplierPrivileges: {
          [hostId]: ['promotions-create'],
        },
        admin: false,
      });
      const payload = {
        name: Math.random().toString(),
        description: '',
        conditions: {
          combinator: 'and',
          leftNode: { combinator: 'equal', leftNode: 'one', rightNode: 1 },
          rightNode: {
            combinator: 'any',
            leftNode: 'objList',
            rightNode: [2, 3],
            negate: true,
            modifier: '_access.c',
          },
        },
        effects: [{ target: 'cart', type: 'absolute', value: 10 }],
        createdById: uuid.v4(),
        updatedById: uuid.v4(),
        startAt: new Date(Date.now() - 1000).toISOString(),
        finishAt: new Date(Date.now() + 1000).toISOString(),
        hostId: undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coupon,
      };

      try {
        await create(nonAdminAuth, payload);
      } catch (err) {
        expect(err.message).to.be.eq(
          'User cannot create promotions without a hostId'
        );
      }
    });
  });
});
