import { expect } from 'chai';
import uuid from 'uuid';
import { promotionFactory } from './index_test';
import { destroy } from '../../../../src/actions/promotions';
import { Promotion } from '../../../../src/models';
import { validAuth } from '../../../fixtures/auth';

describe('Delete Promotion', () => {
  let promotion;
  let hostId;

  beforeEach(async () => {
    hostId = uuid.v4();
    promotion = await promotionFactory({ hostId }).save();
  });

  it('should delete promotion', async () => {
    const auth = validAuth({
      supplierPrivileges: {
        [hostId]: ['promotions-full'],
      },
    });
    await destroy(auth, promotion.id);

    const deletedPromotion = await Promotion.findByPk(promotion.id);

    expect(promotion.id).not.to.equal(null);
    expect(deletedPromotion).to.equal(null);
  });

  it('should NOT delete promotion, user has no access to promotion', async () => {
    const auth = validAuth({
      supplierPrivileges: {
        [hostId]: ['some access'],
      },
    });
    let err;
    try {
      await destroy(auth, promotion.id);
    } catch (e) {
      err = e;
    }

    expect(err.message).to.equal('No privilege');
  });
});
