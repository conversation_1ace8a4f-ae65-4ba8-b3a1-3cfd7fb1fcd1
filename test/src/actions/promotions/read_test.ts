import { expect } from 'chai';
import uuid from 'uuid';
import { read } from '../../../../src/actions/promotions';
import { validAuth } from '../../../fixtures/auth';
import { promotionFactory } from './index_test';

describe('Read Promotions', () => {
  let supplierId;
  let promotion;

  beforeEach(async () => {
    supplierId = uuid.v4();
    promotion = await promotionFactory({
      hostId: supplierId,
      name: 'test promotion',
      startAt: new Date(Date.now() + 1000).toISOString(),
    }).save();
  });

  it('Should fetch promotion', async () => {
    const auth = validAuth({
      supplierPrivileges: {
        [supplierId]: ['promotions-read'],
      },
    });
    const actualResult = await read(auth, promotion.id);

    expect(actualResult.id).to.equal(promotion.id);
    expect(actualResult.name).to.equal('test promotion');
  });

  it('Should NOT fetch promotion, user has no access', async () => {
    const auth = validAuth({
      supplierPrivileges: {
        [supplierId]: ['some-access'],
      },
    });
    let err;
    try {
      await read(auth, promotion.id);
    } catch (e) {
      err = e;
    }

    expect(err.message).to.equal('No privilege');
  });
});
