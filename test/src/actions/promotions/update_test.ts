import { expect } from 'chai';
import uuid from 'uuid';
import { update } from '../../../../src/actions/promotions';
import { validAuth } from '../../../fixtures/auth';
import { promotionFactory } from './index_test';

describe('Update Promotion', () => {
  let auth;
  let hostId;
  let promotion;

  beforeEach(async () => {
    hostId = uuid.v4();
    auth = validAuth({
      supplierPrivileges: {
        [hostId]: ['promotions-update'],
      },
    });
    promotion = await promotionFactory({
      hostId,
      name: 'test promotion',
      startAt: new Date(Date.now() + 1000).toISOString(),
    }).save();
  });

  it('updates promotion with valid data', async () => {
    const updateBody = {
      name: 'edited name',
    };
    const actualResult = await update(auth, promotion.id, updateBody);
    expect(actualResult.name).to.deep.equal('edited name');
  });

  it('updates promotion with one filter', async () => {
    const updateBody = {
      name: 'edited name',
      conditions: [
        {
          field: {
            tag: 'lastOrderedSince',
            isProperty: false,
          },
          operator: 'equals',
          value: '5',
        },
      ],
    };

    const actualResult = await update(auth, promotion.id, updateBody);
    expect(actualResult.name).to.deep.equal('edited name');
    expect(actualResult.conditions).to.deep.equal({
      leftNode: {
        leftNode: 'purchaser',
        tag: 'lastOrderedSince',
        combinator: 'equal',
        modifier: '_access.lastOrderedSince',
        rightNode: '5',
      },
      rightNode: {
        leftNode: 'supplierId',
        combinator: 'equal',
        rightNode: hostId,
      },
      combinator: 'and',
    });
  });

  it('updates promotion with multiple filters', async () => {
    const updateBody = {
      name: 'edited name',
      conditions: [
        {
          field: {
            tag: 'lastOrderedSince',
            isProperty: false,
          },
          operator: 'equals',
          value: '5',
        },
        {
          field: {
            tag: 'firstOrder',
            isProperty: false,
          },
          operator: 'equals',
          value: 'true',
        },
      ],
    };

    const actualResult = await update(auth, promotion.id, updateBody);
    expect(actualResult.name).to.deep.equal('edited name');
    expect(actualResult.conditions).to.deep.equal({
      leftNode: {
        leftNode: {
          leftNode: 'purchaser',
          tag: 'lastOrderedSince',
          combinator: 'equal',
          modifier: '_access.lastOrderedSince',
          rightNode: '5',
        },
        rightNode: {
          leftNode: 'purchaser',
          tag: 'firstOrder',
          combinator: 'equal',
          modifier: '_access.firstOrder',
          rightNode: 'true',
        },
        combinator: 'and',
      },
      rightNode: {
        leftNode: 'supplierId',
        combinator: 'equal',
        rightNode: hostId,
      },
      combinator: 'and',
    });
  });

  it('updates promotion with one filter that is a property', async () => {
    const updateBody = {
      name: 'edited name',
      conditions: [
        {
          field: {
            tag: 'testgroup',
            isProperty: true,
          },
          operator: 'equals',
          value: '5',
        },
      ],
    };

    const actualResult = await update(auth, promotion.id, updateBody);
    expect(actualResult.name).to.deep.equal('edited name');
    expect(actualResult.conditions).to.deep.equal({
      leftNode: {
        leftNode: 'purchaser',
        tag: 'testgroup',
        combinator: 'equal',
        modifier: '_access.properties.testgroup',
        rightNode: '5',
      },
      rightNode: {
        leftNode: 'supplierId',
        combinator: 'equal',
        rightNode: hostId,
      },
      combinator: 'and',
    });
  });

  it('updates promotion with an uppercased coupon', async () => {
    const updateBody = {
      name: 'edited name',
      coupon: 'test1',
    };

    const actualResult = await update(auth, promotion.id, updateBody);
    expect(actualResult.name).to.deep.equal('edited name');
    expect(actualResult.coupon).to.deep.equal('TEST1');
  });

  it('updates promotion sponsored status', async () => {
    const updateBody = {
      name: 'edited name',
      sponsored: true,
    };

    const actualResult = await update(auth, promotion.id, updateBody);
    expect(actualResult.name).to.deep.equal('edited name');
    expect(actualResult.sponsored).to.deep.equal(true);
  });

  it('Should NOT update promotion, user has no access', async () => {
    const updateBody = {
      name: 'edited name',
    };

    auth = validAuth({
      supplierPrivileges: {
        [hostId]: ['some-access'],
      },
    });

    let err;
    try {
      await update(auth, promotion.id, updateBody);
    } catch (e) {
      err = e;
    }

    expect(err.message).to.deep.equal('No privilege');
  });
});
