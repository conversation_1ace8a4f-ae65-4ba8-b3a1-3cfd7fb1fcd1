import { expect } from 'chai';
import uuid from 'uuid';
import moment from 'moment';
import { search } from '../../../../src/actions/promotions';
import { PromotionInstance } from '../../../../src/models/promotion';
import { promotionFactory } from './index_test';
import { validAuth } from '../../../fixtures/auth';

describe('Search Promotions', () => {
  let hostId;
  let auth;

  beforeEach(() => {
    hostId = uuid.v4();
    auth = validAuth({
      supplierPrivileges: {
        [hostId]: ['promotions-read'],
      },
    });
  });

  it('Should fetch promotions with valid query', async () => {
    const query = {
      supplierId: hostId,
      pageNo: 1,
      pageSize: 50,
      offset: 0,
    };

    const promotion = await promotionFactory({
      hostId,
      name: 'test promotion',
      startAt: new Date(Date.now() + 1000).toISOString(),
    }).save();

    await promotionFactory({
      hostId: uuid.v4(),
      name: 'test promotion from other supplier',
      startAt: new Date(Date.now() + 1000).toISOString(),
    }).save();

    const promotions = await search(auth, query);
    expect(promotions.data.length).to.equal(1);
    expect(promotions.data[0].id).to.equal(promotion.id);
    expect(promotions.data[0].name).to.equal('test promotion');
  });

  it('Should fetch promotions with correct pagination meta data', async () => {
    const query = {
      supplierId: hostId,
      pageNo: 1,
      pageSize: 5,
      offset: 0,
    };

    const generatePromotions: Promise<PromotionInstance>[] = [];
    for (let i = 0; i < 10; i += 1) {
      generatePromotions.push(
        promotionFactory({
          hostId: query.supplierId,
          name: `test promotion - ${i}`,
          startAt: new Date(Date.now() + 1000).toISOString(),
        }).save()
      );
    }

    await Promise.all(generatePromotions);

    const actual = await search(auth, query);
    expect(actual.data.length).to.equal(5);
    expect(actual.meta.totalResults).to.equal(10);
    expect(actual.meta.totalPages).to.equal(2);
    expect(actual.meta.pageSize).to.equal(5);
    expect(actual.meta.pageNo).to.equal(1);
  });

  it('Should fetch promotions created yesterday', async () => {
    const today = moment();

    const yesterdayPromotion = await promotionFactory({
      hostId,
      name: 'test promotion yesterday',
      createdAt: moment().subtract(1, 'day').toISOString(),
    }).save();

    const query = {
      supplierId: hostId,
      pageNo: 1,
      pageSize: 50,
      offset: 0,
      createdAt: {
        lte: yesterdayPromotion.createdAt,
      },
    };

    await promotionFactory({
      hostId: query.supplierId,
      name: 'test promotion today',
      startAt: today.toISOString(),
      createdAt: today.toISOString(),
    }).save();

    const promotions = await search(auth, query);

    expect(promotions.data.length).to.equal(1);
    // expect(promotions.data[0].createdAt).to.equal(yesterdayPromotion.createdAt);

    expect(promotions.data[0].id).to.equal(yesterdayPromotion.id);
    expect(promotions.data[0].name).to.equal('test promotion yesterday');
  });

  it('Should fetch promotions updated yesterday', async () => {
    const today = moment();

    const yesterdayPromotion = await promotionFactory({
      hostId,
      name: 'test promotion yesterday',
      updatedAt: moment().subtract(1, 'day').toISOString(),
    }).save();

    const query = {
      supplierId: hostId,
      pageNo: 1,
      pageSize: 50,
      offset: 0,
      updatedAt: {
        lte: yesterdayPromotion.updatedAt,
      },
    };

    await promotionFactory({
      hostId: query.supplierId,
      name: 'test promotion today',
      startAt: today.toISOString(),
      updatedAt: today.toISOString(),
    }).save();

    const promotions = await search(auth, query);

    expect(promotions.data.length).to.equal(1);
    expect(promotions.data[0].id).to.equal(yesterdayPromotion.id);
    expect(promotions.data[0].name).to.equal('test promotion yesterday');
  });

  it('Should fetch promotions with exact createdAt date', async () => {
    const promotion = await promotionFactory({
      hostId,
      name: 'test promotion',
      createdAt: moment().subtract(1, 'day').toISOString(),
    }).save();

    await promotionFactory({
      hostId,
      name: 'test promotion 2',
    }).save();

    const query = {
      supplierId: hostId,
      pageNo: 1,
      pageSize: 50,
      offset: 0,
      createdAt: {
        eq: promotion.createdAt,
      },
    };

    const promotions = await search(auth, query);

    expect(promotions.data.length).to.equal(1);
    expect(promotions.data[0].id).to.equal(promotion.id);
    expect(promotions.data[0].name).to.equal('test promotion');
  });

  it('Should NOT fetch promotions, user has no access', async () => {
    const query = {
      supplierId: uuid.v4(),
      pageNo: 1,
      pageSize: 50,
      offset: 0,
    };

    let err;

    try {
      await search(auth, query);
    } catch (e) {
      err = e;
    }

    expect(err.message).to.equal('No privilege');
  });

  it('Should throw validation error due to invalid date range query', async () => {
    const query = {
      supplierId: hostId,
      pageNo: 1,
      pageSize: 50,
      offset: 0,
      createdAt: {
        gte: 'not a date string',
      },
      updatedAt: {
        lt: 'not a date string',
      },
    };

    let err;

    try {
      await search(auth, query);
    } catch (e) {
      err = e;
    }

    expect(err.message).to.equal('"createdAt.gte" must be a valid date');
  });
});
