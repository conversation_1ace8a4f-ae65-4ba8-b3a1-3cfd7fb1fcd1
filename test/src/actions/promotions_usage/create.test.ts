import uuid from 'uuid';
import { expect } from 'chai';
import { Authorization } from '@ordermentum/auth-driver';

import { validAuth } from '../../../fixtures/auth';
import * as PromotionUsage from '../../../../src/actions/promotions_usage/create';
import createPromotion from '../../../../src/actions/promotions/create';

describe('Create Promotion Usage', () => {
  let hostId: string;
  let auth: Authorization;
  let retailerId: string;
  let userId: string;
  let supplierId: string;
  let purchaserId: string;
  let coupon: string;
  let payload;
  const asapClaims = {
    userId: uuid.v4(),
    admin: true,
  };

  beforeEach(() => {
    hostId = uuid.v4();
    coupon =
      Math.random().toString(36).substring(0, 5) +
      Math.random().toString(36).substring(0, 5);
    coupon = coupon.toUpperCase();
    auth = validAuth({
      supplierPrivileges: {
        [hostId]: ['promotions-create'],
      },
    });
    retailerId = uuid.v4();
    userId = uuid.v4();
    supplierId = uuid.v4();
    purchaserId = uuid.v4();
    payload = {
      name: Math.random().toString(),
      description: '',
      conditions: [],
      effects: [{ target: 'cart', type: 'absolute', value: 10 }],
      createdById: uuid.v4(),
      updatedById: uuid.v4(),
      startAt: new Date(Date.now() - 1000).toISOString(),
      finishAt: new Date(Date.now() + 1000).toISOString(),
      hostId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // This is not a UUID in reality
      // because it is a random string generated by the client
      // but we are using it for uniqueness in testing
      coupon,
      usage: 0,
      sponsored: false,
    };
  });

  it('should create a promotion usage', async () => {
    const promotionData = await createPromotion(auth, payload);
    const [promotionUsageData] = await PromotionUsage.create({
      retailerId,
      userId,
      supplierId,
      purchaserId,
      discounts: [
        {
          name: payload.name,
          coupon: payload.coupon,
          hostId: payload.hostId,
          sponsored: payload.sponsored,
        },
      ],
    });

    expect(promotionUsageData.get('supplierId')).to.be.eq(supplierId);
    expect(promotionUsageData.get('retailerId')).to.be.eq(retailerId);
    expect(promotionUsageData.get('userId')).to.be.eq(userId);
    expect(promotionUsageData.get('promotionId')).to.be.eq(promotionData.id);
    expect(promotionUsageData.get('purchaserId')).to.be.eq(purchaserId);
  });

  it('should throw an error if arguments is not valid', async () => {
    await createPromotion(auth, payload);
    try {
      await PromotionUsage.create({
        retailerId: '',
        userId: '',
        supplierId: '',
        purchaserId: '',
        discounts: [
          {
            name: payload.name,
            coupon: payload.coupon,
            hostId: payload.hostId,
            sponsored: false,
          },
        ],
      });
    } catch (e) {
      expect(e.name).to.be.eq('BadRequestError');
    }
  });

  it('should throw an error if purchaser id is not valid', async () => {
    await createPromotion(auth, payload);
    try {
      await PromotionUsage.create({
        retailerId,
        userId,
        supplierId,
        purchaserId: '',
        discounts: [
          {
            name: payload.name,
            coupon,
            hostId: payload.hostId,
            sponsored: payload.sponsored,
          },
        ],
      });
    } catch (e) {
      expect(e.message).to.be.eq('"purchaserId" is not allowed to be empty');
    }
  });

  it('should throw an error if discount is not valid', async () => {
    await createPromotion(auth, payload);
    try {
      await PromotionUsage.create({
        retailerId,
        userId,
        supplierId,
        purchaserId: '',
        discounts: [
          {
            name: 'wrong coupon',
            coupon: payload.coupon,
            hostId: payload.hostId,
            sponsored: payload.sponsored,
          },
        ],
      });
    } catch (e) {
      expect(e.message).to.be.eq('Promotion not found');
    }
  });
});
