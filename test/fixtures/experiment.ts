import faker from 'faker';
import models from '../../src/models';
import { uniqueName } from '../helper';

const base = {
  meta: {},
  priority: 0,
  active: true,
  exclusive: false,
  limit: 0,
  interval: 'total',
  audienceIds: [],
};

const validExperiment = async params => {
  const attrs = {
    ...base,
    name: uniqueName(),
    ...params,
  };
  const experiment = await models.Experiment.create(attrs);

  if (params.audienceIds) {
    for (const id of params.audienceIds) {
      /* eslint-disable no-await-in-loop */
      await models.ExperimentAudience.create({
        audienceId: id,
        experimentId: experiment.id,
      });
    }
    return experiment.reload({
      include: [
        {
          model: models.Audience,
          as: 'audiences',
        },
      ],
    });
  }
  return experiment;
};

export default validExperiment;
