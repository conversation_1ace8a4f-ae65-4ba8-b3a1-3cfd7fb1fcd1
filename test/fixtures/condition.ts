import faker from 'faker';
import validAudience from './audience';
import models from '../../src/models';

const base = {
  key: 'supplierId',
  operator: 'includes',
  rval: ['123', '234'],
};

const validCondition = async params => {
  const audience = await validAudience({
    name: faker.name.findName(),
  });
  const attrs = {
    ...base,
    audienceId: audience.id,
    ...params,
  };
  const condition = await models.Condition.create(attrs);
  return condition;
};

export default validCondition;
