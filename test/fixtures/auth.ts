import { Authorization } from '@ordermentum/auth-driver';
import uuid from 'uuid';

export const validAuth = (context?: any) =>
  new Authorization({
    id: uuid.v4(),
    email: `${uuid.v4()}@test.com`,
    emails: [`${uuid.v4()}@test.com`],
    created_at: new Date().toISOString(),
    name: { full: 'test', firstName: 'test', lastName: 'test' },
    firstName: 'test',
    lastName: 'test',
    password: 'test',
    metadata: { type: '' },
    permissions: [],
    context: {
      suppliers: [],
      retailers: [],
      supplierPrivileges: {},
      retailerPrivileges: {},
      admin: false,
      superAdmin: false,
      ...context,
    },
    userRoles: [],
    admin: false,
    superAdmin: false,
  });
