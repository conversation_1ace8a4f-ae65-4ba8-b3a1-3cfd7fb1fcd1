version: "2"
services:
  common:
    build:
      context: .
      args:
        NPM_AUTH_TOKEN: ${NPM_CI_TOKEN}
    volumes:
      - .:/usr/src/app
    environment:
      - DATABASE_INITIALIZATION_URI=postgres://postgres@postgresql/template1
      - REDIS_URL=redis
      - AUTH_URL=*********************
      - JWT_SECRET=testsecret
      - SENTRY_DSN=https://1c1855bb97314aa198672da168148da7:<EMAIL>/29290
      - SECRET_KEY=stale_aardvark_tarnish_regulate_nash
      - NEW_RELIC_ENABLED=false
      - NEW_RELIC_NO_CONFIG_FILE=true
  test:
    extends: common
    build: .
    command: npm run spec
    volumes:
      - .:/usr/src/app
    environment:
      - PORT=6446
      - LOG_LEVEL=fatal
      - AUTH_URL=*********************
      - JWT_SECRET=testsecret
      - REDIS_URL=redis
      - DATABASE_URI=postgres://ordermentum@postgresql/flags_testing
      - NODE_ENV=test
  app:
    extends: common
    build: .
    volumes:
      - .:/usr/src/app
    command: watchexec -i "build/*" -e ts,js -r npm run start:dev | ./node_modules/.bin/bunyan
    environment:
      - PORT=6446
      - AUTH_URL=*********************
      - JWT_SECRET=testsecret
      - REDIS_URL=redis
      - DATABASE_URI=postgres://ordermentum@postgresql/flags_development
      - NODE_ENV=development
    ports:
      - "6446:6446"
    networks:
      default:
        aliases:
          - flags_service
networks:
  default:
    external:
      name: oaknuggins_default
