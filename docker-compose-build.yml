version: "2"
services:
  kubesdeployer:
    image: ordermentum/infrastructure:latest
    volumes:
      - .:/opt/src
    environment:
      - K8S_CONFIG=${K8S_CONFIG}
      - K8S_NAMESPACE=${K8S_NAMESPACE}
      - BUILDKITE=${BUILDKITE}
      - BUILDKITE_REPO=${BUILDKITE_REPO}
      - BUILDKITE_BRANCH=${BUILDKITE_BRANCH}
      - BUILDKITE_TAG=${BUILDKITE_TAG}
      - BUILDKITE_COMMIT=${BUILDKITE_COMMIT}
      - BUILDKITE_JOB_ID=${BUILDKITE_JOB_ID}
      - BUILDKITE_BUILD_ID=${BUILDKITE_BUILD_ID}
      - BUILDKITE_BUILD_URL=${BUILDKITE_BUILD_URL}
      - BUILDKITE_AGENT_NAME=${BUILDKITE_AGENT_NAME}
      - B<PERSON><PERSON>KITE_COMMAND=${BUILDKITE_COMMAND}
      - B<PERSON>LDKITE_MESSAGE=${BUILDKITE_MESSAGE}
      - B<PERSON>LDKITE_TIMEOUT=${BUILDKITE_TIMEOUT}
      - BUILDKITE_BUILD_NUMBER=${BUILDKITE_BUILD_NUMBER}
      - BUILDKITE_ORGANIZATION_SLUG=${BUILDKITE_ORGANIZATION_SLUG}
      - BUILDKITE_PIPELINE_SLUG=${BUILDKITE_PIPELINE_SLUG}
      - BUILDKITE_PIPELINE_PROVIDER=${BUILDKITE_PIPELINE_PROVIDER}
      - BUILDKITE_PIPELINE_DEFAULT_BRANCH=${BUILDKITE_PIPELINE_DEFAULT_BRANCH}
      - BUILDKITE_PULL_REQUEST=${BUILDKITE_PULL_REQUEST}
      - BUILDKITE_ARTIFACT_PATHS=${BUILDKITE_ARTIFACT_PATHS}
      - BUILDKITE_BUILD_CREATOR=${BUILDKITE_BUILD_CREATOR}
      - BUILDKITE_BUILD_CREATOR_EMAIL=${BUILDKITE_BUILD_CREATOR_EMAIL}
      - BUILDKITE_CLEAN_CHECKOUT=${BUILDKITE_CLEAN_CHECKOUT}
      - BUILDKITE_BUILD_CHECKOUT_PATH=${BUILDKITE_BUILD_CHECKOUT_PATH}
      - BUILDKITE_BUILD_CREATOR_TEAMS=${BUILDKITE_BUILD_CREATOR_TEAMS}
  common:
    build:
      context: .
      args:
        NPM_AUTH_TOKEN: ${NPM_CI_TOKEN}
    volumes:
      - .:/usr/src/app
  postgresql:
    image: postgres:9.6
    command: postgres
    volumes:
      - ./migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_HOST_AUTH_METHOD=trust
  redis:
    image: redis
    ports:
      - "6379:6379"
  test:
    extends: common
    command: npm run spec
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    environment:
      - NODE_ENV=test
      - PORT=6446
      - REDIS_URL=redis
      - AUTH_URL=*********************
      - JWT_SECRET=testsecret
      - DATABASE_INITIALIZATION_URI=postgres://postgres@postgresql/template1
      - DATABASE_URI=postgres://ordermentum@postgresql/flags_testing
      - BUILDKITE=${BUILDKITE}
      - BUILDKITE_REPO=${BUILDKITE_REPO}
      - BUILDKITE_BRANCH=${BUILDKITE_BRANCH}
      - BUILDKITE_TAG=${BUILDKITE_TAG}
      - BUILDKITE_COMMIT=${BUILDKITE_COMMIT}
      - BUILDKITE_JOB_ID=${BUILDKITE_JOB_ID}
      - BUILDKITE_BUILD_ID=${BUILDKITE_BUILD_ID}
      - BUILDKITE_BUILD_URL=${BUILDKITE_BUILD_URL}
      - BUILDKITE_AGENT_NAME=${BUILDKITE_AGENT_NAME}
      - BUILDKITE_COMMAND=${BUILDKITE_COMMAND}
      - BUILDKITE_MESSAGE=${BUILDKITE_MESSAGE}
      - BUILDKITE_TIMEOUT=${BUILDKITE_TIMEOUT}
      - BUILDKITE_BUILD_NUMBER=${BUILDKITE_BUILD_NUMBER}
      - BUILDKITE_ORGANIZATION_SLUG=${BUILDKITE_ORGANIZATION_SLUG}
      - BUILDKITE_PIPELINE_SLUG=${BUILDKITE_PIPELINE_SLUG}
      - BUILDKITE_PIPELINE_PROVIDER=${BUILDKITE_PIPELINE_PROVIDER}
      - BUILDKITE_PIPELINE_DEFAULT_BRANCH=${BUILDKITE_PIPELINE_DEFAULT_BRANCH}
      - BUILDKITE_PULL_REQUEST=${BUILDKITE_PULL_REQUEST}
      - BUILDKITE_ARTIFACT_PATHS=${BUILDKITE_ARTIFACT_PATHS}
      - BUILDKITE_BUILD_CREATOR=${BUILDKITE_BUILD_CREATOR}
      - BUILDKITE_BUILD_CREATOR_EMAIL=${BUILDKITE_BUILD_CREATOR_EMAIL}
      - BUILDKITE_CLEAN_CHECKOUT=${BUILDKITE_CLEAN_CHECKOUT}
      - BUILDKITE_BUILD_CHECKOUT_PATH=${BUILDKITE_BUILD_CHECKOUT_PATH}
      - BUILDKITE_BUILD_CREATOR_TEAMS=${BUILDKITE_BUILD_CREATOR_TEAMS}
      - SECRET_KEY=stale_aardvark_tarnish_regulate_nash
      - SENTRY_DSN=https://just.to.mock/you/buildkite

    links:
      - postgresql
      - redis
    depends_on:
      - postgresql
      - redis
