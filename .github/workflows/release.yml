name: Release Drafter

on:
  push:
    branches:
      - develop

permissions:
  contents: write
  pull-requests: write

jobs:
  update_release_draft:
    runs-on: ubuntu-latest
    steps:
      - name: Set version
        id: version
        run: |
          RELEASE_VERSION=v$(TZ='Australia/Sydney' date +'%Y.%m.%d-%H%M')
          echo "RELEASE_VERSION=$RELEASE_VERSION" >> $GITHUB_OUTPUT
      - name: Create draft
        uses: release-drafter/release-drafter@v6
        with:
          config-name: release-drafter.yml
          version: ${{ steps.version.outputs.RELEASE_VERSION }}
          name: ${{ steps.version.outputs.RELEASE_VERSION }}
          tag: ${{ steps.version.outputs.RELEASE_VERSION }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
